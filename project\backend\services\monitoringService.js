import os from 'os';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { EventEmitter } from 'events';
import { exec } from 'child_process';
import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import { NotificationService } from './notificationService.js';

const writeFile = promisify(fs.writeFile);
const appendFile = promisify(fs.appendFile);
const execAsync = promisify(exec);

export class MonitoringService extends EventEmitter {
  constructor(config = {}) {
    super();

    this.config = {
      intervals: {
        system: config.intervals?.system || 30000,    // 30 seconds
        services: config.intervals?.services || 60000, // 1 minute
        network: config.intervals?.network || 120000,  // 2 minutes
        alerts: config.intervals?.alerts || 10000      // 10 seconds
      },
      thresholds: {
        cpu: config.thresholds?.cpu || 80,           // 80%
        memory: config.thresholds?.memory || 85,     // 85%
        disk: config.thresholds?.disk || 90,         // 90%
        network: config.thresholds?.network || 1000, // 1000 Mbps
        responseTime: config.thresholds?.responseTime || 5000 // 5 seconds
      },
      alerts: {
        enabled: config.alerts?.enabled || true,
        channels: config.alerts?.channels || ['email', 'dashboard'],
        cooldown: config.alerts?.cooldown || 300000 // 5 minutes
      },
      retention: {
        metrics: config.retention?.metrics || 7,     // 7 days
        alerts: config.retention?.alerts || 30      // 30 days
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.notificationService = new NotificationService();

    // Legacy properties for backward compatibility
    this.metricsDir = path.join(process.cwd(), 'logs', 'metrics');
    this.alertRules = new Map();
    this.metrics = {
      cpu: [],
      memory: [],
      requests: [],
      errors: [],
      responseTime: []
    };
    this.alertThresholds = this.config.thresholds; // Use new thresholds
    this.metricsInterval = this.config.intervals.system;
    this.retentionPeriod = this.config.retention.metrics * 24 * 60 * 60 * 1000;
    this.notificationHandlers = new Set();

    // Enhanced monitoring data
    this.realTimeMetrics = new Map();
    this.alerts = new Map();
    this.services = new Map();
    this.nodes = new Map();
    this.alertCooldowns = new Map();

    // Monitoring state
    this.isMonitoring = false;
    this.intervals = new Map();

    this.ensureMetricsDirectory();
    this.initializeMonitoring();
  }

  async ensureMetricsDirectory() {
    if (!fs.existsSync(this.metricsDir)) {
      fs.mkdirSync(this.metricsDir, { recursive: true });
    }
  }

  async initializeMonitoring() {
    try {
      // Register default services to monitor
      this.registerService('nginx', { port: 80, healthCheck: '/health' });
      this.registerService('mysql', { port: 3306, type: 'tcp' });
      this.registerService('mongodb', { port: 27017, type: 'tcp' });
      this.registerService('redis', { port: 6379, type: 'tcp' });

      // Start monitoring
      await this.startRealTimeMonitoring();

      // Start legacy metrics collection for backward compatibility
      this.startMetricsCollection();

      logger.info('Enhanced monitoring service initialized successfully');
    } catch (error) {
      logger.error('Error initializing monitoring service:', error);
    }
  }

  async startRealTimeMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Start system metrics collection
    this.intervals.set('system', setInterval(() => {
      this.collectEnhancedSystemMetrics();
    }, this.config.intervals.system));

    // Start service monitoring
    this.intervals.set('services', setInterval(() => {
      this.monitorServices();
    }, this.config.intervals.services));

    // Start network monitoring
    this.intervals.set('network', setInterval(() => {
      this.monitorNetwork();
    }, this.config.intervals.network));

    // Start alert processing
    this.intervals.set('alerts', setInterval(() => {
      this.processAlerts();
    }, this.config.intervals.alerts));

    logger.info('Real-time monitoring started');

    await this.auditService.log(
      null,
      'monitoring_started',
      { intervals: this.config.intervals },
      'info'
    );
  }

  async stopRealTimeMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    // Clear all intervals
    for (const [name, interval] of this.intervals) {
      clearInterval(interval);
    }
    this.intervals.clear();

    logger.info('Real-time monitoring stopped');

    await this.auditService.log(
      null,
      'monitoring_stopped',
      {},
      'warning'
    );
  }

  startMetricsCollection() {
    setInterval(() => this.collectMetrics(), this.metricsInterval);
  }

  async collectMetrics() {
    const timestamp = new Date().toISOString();
    const metrics = {
      timestamp,
      cpu: this.getCPUUsage(),
      memory: this.getMemoryUsage(),
      system: this.getSystemMetrics()
    };

    await this.saveMetrics(metrics);
    await this.checkAlertRules(metrics);
    this.pruneOldMetrics();

    this.emit('metrics', metrics);
    return metrics;
  }

  getCPUUsage() {
    const cpus = os.cpus();
    const usage = cpus.reduce((acc, cpu) => {
      const total = Object.values(cpu.times).reduce((a, b) => a + b);
      const idle = cpu.times.idle;
      return acc + ((total - idle) / total) * 100;
    }, 0) / cpus.length;

    return Math.round(usage * 100) / 100;
  }

  getMemoryUsage() {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    return Math.round((used / total) * 100 * 100) / 100;
  }

  getSystemMetrics() {
    return {
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      platform: os.platform(),
      release: os.release()
    };
  }

  async saveMetrics(metrics) {
    const metricsFile = path.join(
      this.metricsDir,
      `metrics-${metrics.timestamp.split('T')[0]}.json`
    );

    try {
      const metricsLine = JSON.stringify(metrics) + '\n';
      await appendFile(metricsFile, metricsLine);
    } catch (error) {
      console.error('Failed to save metrics:', error);
    }
  }

  async getMetrics(startDate, endDate) {
    const files = await fs.promises.readdir(this.metricsDir);
    const metrics = [];

    for (const file of files) {
      if (!file.endsWith('.json')) continue;

      const content = await fs.promises.readFile(
        path.join(this.metricsDir, file),
        'utf8'
      );

      const entries = content
        .trim()
        .split('\n')
        .map(line => JSON.parse(line))
        .filter(entry => {
          const timestamp = new Date(entry.timestamp);
          return (!startDate || timestamp >= new Date(startDate)) &&
                 (!endDate || timestamp <= new Date(endDate));
        });

      metrics.push(...entries);
    }

    return metrics;
  }

  setAlertRule(name, condition, action) {
    this.alertRules.set(name, { condition, action });
  }

  async checkAlertRules(metrics) {
    for (const [name, rule] of this.alertRules) {
      try {
        if (rule.condition(metrics)) {
          const alert = {
            name,
            timestamp: new Date().toISOString(),
            metrics
          };
          await this.triggerAlert(alert);
        }
      } catch (error) {
        console.error(`Error checking alert rule ${name}:`, error);
      }
    }
  }

  async triggerAlert(alert) {
    const notifications = Array.from(this.notificationHandlers).map(handler =>
      handler(alert).catch(error =>
        console.error('Notification handler failed:', error)
      )
    );

    await Promise.allSettled(notifications);
    this.emit('alert', alert);
  }

  registerNotificationHandler(handler) {
    this.notificationHandlers.add(handler);
  }

  pruneOldMetrics() {
    const cutoffDate = new Date(Date.now() - this.retentionPeriod);
    Object.values(this.metrics).forEach(metricArray => {
      const index = metricArray.findIndex(m => new Date(m.timestamp) >= cutoffDate);
      if (index > 0) {
        metricArray.splice(0, index);
      }
    });
  }

  getAggregatedMetrics(startDate, endDate) {
    return this.getMetrics(startDate, endDate).then(metrics => {
      const aggregated = {
        cpu: {
          avg: 0,
          max: 0,
          min: Infinity
        },
        memory: {
          avg: 0,
          max: 0,
          min: Infinity
        },
        timeRange: {
          start: startDate,
          end: endDate
        }
      };

      metrics.forEach(metric => {
        // CPU metrics
        aggregated.cpu.avg += metric.cpu;
        aggregated.cpu.max = Math.max(aggregated.cpu.max, metric.cpu);
        aggregated.cpu.min = Math.min(aggregated.cpu.min, metric.cpu);

        // Memory metrics
        aggregated.memory.avg += metric.memory;
        aggregated.memory.max = Math.max(aggregated.memory.max, metric.memory);
        aggregated.memory.min = Math.min(aggregated.memory.min, metric.memory);
      });

      const count = metrics.length;
      if (count > 0) {
        aggregated.cpu.avg /= count;
        aggregated.memory.avg /= count;
      }

      return aggregated;
    });
  }

  // ===== ENHANCED MONITORING METHODS =====

  async collectEnhancedSystemMetrics() {
    try {
      const timestamp = new Date();
      const metrics = {
        timestamp,
        cpu: await this.getEnhancedCPUUsage(),
        memory: await this.getEnhancedMemoryUsage(),
        disk: await this.getDiskUsage(),
        network: await this.getNetworkUsage(),
        load: os.loadavg(),
        uptime: os.uptime(),
        processes: await this.getProcessCount()
      };

      // Store metrics
      const metricsKey = `system_${timestamp.getTime()}`;
      this.realTimeMetrics.set(metricsKey, metrics);

      // Check thresholds and trigger alerts
      await this.checkSystemThresholds(metrics);

      // Emit metrics event
      this.emit('enhancedMetrics', { type: 'system', data: metrics });

      // Cleanup old metrics
      this.cleanupOldMetrics();

    } catch (error) {
      logger.error('Error collecting enhanced system metrics:', error);
    }
  }

  async getEnhancedCPUUsage() {
    try {
      const { stdout } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
      return parseFloat(stdout.trim()) || 0;
    } catch (error) {
      return this.getCPUUsage(); // Fallback to legacy method
    }
  }

  async getEnhancedMemoryUsage() {
    try {
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;

      return {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        percentage: (usedMem / totalMem) * 100
      };
    } catch (error) {
      const legacyUsage = this.getMemoryUsage();
      return {
        total: os.totalmem(),
        used: os.totalmem() - os.freemem(),
        free: os.freemem(),
        percentage: legacyUsage
      };
    }
  }

  async getDiskUsage() {
    try {
      const { stdout } = await execAsync("df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1");
      const percentage = parseFloat(stdout.trim()) || 0;

      const { stdout: sizeInfo } = await execAsync("df -h / | awk 'NR==2{print $2,$3,$4}'");
      const [total, used, available] = sizeInfo.trim().split(' ');

      return {
        total,
        used,
        available,
        percentage
      };
    } catch (error) {
      return { total: '0G', used: '0G', available: '0G', percentage: 0 };
    }
  }

  async getNetworkUsage() {
    try {
      const { stdout } = await execAsync("cat /proc/net/dev | grep eth0 | awk '{print $2,$10}'");
      const [rxBytes, txBytes] = stdout.trim().split(' ').map(Number);

      return {
        rxBytes: rxBytes || 0,
        txBytes: txBytes || 0,
        timestamp: Date.now()
      };
    } catch (error) {
      return { rxBytes: 0, txBytes: 0, timestamp: Date.now() };
    }
  }

  async getProcessCount() {
    try {
      const { stdout } = await execAsync("ps aux | wc -l");
      return parseInt(stdout.trim()) - 1; // Subtract header line
    } catch (error) {
      return 0;
    }
  }

  async checkSystemThresholds(metrics) {
    const alerts = [];

    // CPU threshold
    if (metrics.cpu > this.config.thresholds.cpu) {
      alerts.push({
        type: 'cpu_high',
        severity: 'warning',
        message: `CPU usage is ${metrics.cpu.toFixed(1)}% (threshold: ${this.config.thresholds.cpu}%)`,
        value: metrics.cpu,
        threshold: this.config.thresholds.cpu
      });
    }

    // Memory threshold
    if (metrics.memory.percentage > this.config.thresholds.memory) {
      alerts.push({
        type: 'memory_high',
        severity: 'warning',
        message: `Memory usage is ${metrics.memory.percentage.toFixed(1)}% (threshold: ${this.config.thresholds.memory}%)`,
        value: metrics.memory.percentage,
        threshold: this.config.thresholds.memory
      });
    }

    // Disk threshold
    if (metrics.disk.percentage > this.config.thresholds.disk) {
      alerts.push({
        type: 'disk_high',
        severity: 'critical',
        message: `Disk usage is ${metrics.disk.percentage}% (threshold: ${this.config.thresholds.disk}%)`,
        value: metrics.disk.percentage,
        threshold: this.config.thresholds.disk
      });
    }

    // Process alerts
    for (const alert of alerts) {
      await this.triggerEnhancedAlert(alert);
    }
  }

  async triggerEnhancedAlert(alertData) {
    const alertId = `${alertData.type}_${Date.now()}`;
    const cooldownKey = alertData.type;

    // Check cooldown
    if (this.alertCooldowns.has(cooldownKey)) {
      const lastAlert = this.alertCooldowns.get(cooldownKey);
      if (Date.now() - lastAlert < this.config.alerts.cooldown) {
        return; // Skip alert due to cooldown
      }
    }

    const alert = {
      id: alertId,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false,
      ...alertData
    };

    // Store alert
    this.alerts.set(alertId, alert);
    this.alertCooldowns.set(cooldownKey, Date.now());

    // Send notifications
    if (this.config.alerts.enabled) {
      await this.sendAlertNotifications(alert);
    }

    // Emit alert event
    this.emit('enhancedAlert', alert);

    // Log alert
    await this.auditService.log(
      null,
      'monitoring_alert_triggered',
      alert,
      alert.severity === 'critical' ? 'error' : 'warning'
    );

    logger.warn(`Alert triggered: ${alert.message}`, { alertId, type: alert.type });
  }

  async sendAlertNotifications(alert) {
    try {
      const message = `🚨 ${alert.severity.toUpperCase()}: ${alert.message}`;

      for (const channel of this.config.alerts.channels) {
        switch (channel) {
          case 'email':
            // Would send email notification
            logger.info(`Email alert sent: ${alert.type}`);
            break;
          case 'sms':
            // Would send SMS notification
            logger.info(`SMS alert sent: ${alert.type}`);
            break;
          case 'dashboard':
            // Dashboard notification handled by frontend
            break;
        }
      }
    } catch (error) {
      logger.error('Error sending alert notifications:', error);
    }
  }

  async monitorServices() {
    try {
      for (const [serviceName, serviceConfig] of this.services) {
        const status = await this.checkServiceHealth(serviceName, serviceConfig);

        // Store service status
        this.services.set(serviceName, { ...serviceConfig, ...status, lastChecked: new Date() });

        // Check for service failures
        if (!status.healthy) {
          await this.triggerEnhancedAlert({
            type: 'service_down',
            severity: 'critical',
            message: `Service ${serviceName} is down: ${status.error}`,
            service: serviceName,
            error: status.error
          });
        }

        // Emit service status event
        this.emit('serviceStatus', { service: serviceName, status });
      }
    } catch (error) {
      logger.error('Error monitoring services:', error);
    }
  }

  async checkServiceHealth(serviceName, config) {
    try {
      if (config.type === 'tcp') {
        return await this.checkTCPPort(config.port);
      } else if (config.healthCheck) {
        return await this.checkHTTPHealth(config.port, config.healthCheck);
      } else {
        return await this.checkProcessRunning(serviceName);
      }
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        responseTime: 0
      };
    }
  }

  async checkTCPPort(port) {
    try {
      const { stdout } = await execAsync(`netstat -tuln | grep :${port}`);
      return {
        healthy: stdout.includes(`${port}`),
        responseTime: 0
      };
    } catch (error) {
      return {
        healthy: false,
        error: `Port ${port} not listening`,
        responseTime: 0
      };
    }
  }

  async checkHTTPHealth(port, path) {
    try {
      const startTime = Date.now();
      const response = await fetch(`http://localhost:${port}${path}`, {
        timeout: this.config.thresholds.responseTime
      });
      const responseTime = Date.now() - startTime;

      return {
        healthy: response.ok,
        responseTime,
        statusCode: response.status
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        responseTime: this.config.thresholds.responseTime
      };
    }
  }

  async checkProcessRunning(processName) {
    try {
      const { stdout } = await execAsync(`pgrep -f ${processName}`);
      return {
        healthy: stdout.trim().length > 0,
        responseTime: 0
      };
    } catch (error) {
      return {
        healthy: false,
        error: `Process ${processName} not running`,
        responseTime: 0
      };
    }
  }

  async monitorNetwork() {
    try {
      const networkStats = await this.getDetailedNetworkStats();

      // Store network metrics
      const timestamp = new Date();
      this.realTimeMetrics.set(`network_${timestamp.getTime()}`, {
        timestamp,
        ...networkStats
      });

      // Check for network issues
      if (networkStats.packetLoss > 5) {
        await this.triggerEnhancedAlert({
          type: 'network_packet_loss',
          severity: 'warning',
          message: `High packet loss detected: ${networkStats.packetLoss}%`,
          value: networkStats.packetLoss
        });
      }

      this.emit('networkMetrics', networkStats);
    } catch (error) {
      logger.error('Error monitoring network:', error);
    }
  }

  async getDetailedNetworkStats() {
    try {
      // Get interface statistics
      const { stdout } = await execAsync("cat /proc/net/dev | grep -E '(eth0|ens|enp)'");
      const lines = stdout.trim().split('\n');

      const stats = {
        interfaces: [],
        totalRx: 0,
        totalTx: 0,
        packetLoss: 0
      };

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        const interfaceName = parts[0].replace(':', '');
        const rxBytes = parseInt(parts[1]) || 0;
        const txBytes = parseInt(parts[9]) || 0;

        stats.interfaces.push({
          name: interfaceName,
          rxBytes,
          txBytes
        });

        stats.totalRx += rxBytes;
        stats.totalTx += txBytes;
      }

      return stats;
    } catch (error) {
      return {
        interfaces: [],
        totalRx: 0,
        totalTx: 0,
        packetLoss: 0
      };
    }
  }

  async processAlerts() {
    try {
      // Auto-resolve alerts that are no longer relevant
      for (const [alertId, alert] of this.alerts) {
        if (!alert.resolved && this.shouldAutoResolve(alert)) {
          await this.resolveAlert(alertId, 'auto-resolved');
        }
      }

      // Cleanup old alerts
      this.cleanupOldAlerts();
    } catch (error) {
      logger.error('Error processing alerts:', error);
    }
  }

  shouldAutoResolve(alert) {
    // Auto-resolve logic based on alert type and current metrics
    const latestMetrics = this.getLatestSystemMetrics();
    if (!latestMetrics) return false;

    switch (alert.type) {
      case 'cpu_high':
        return latestMetrics.cpu < this.config.thresholds.cpu * 0.8;
      case 'memory_high':
        return latestMetrics.memory.percentage < this.config.thresholds.memory * 0.8;
      case 'disk_high':
        return latestMetrics.disk.percentage < this.config.thresholds.disk * 0.8;
      default:
        return false;
    }
  }

  async resolveAlert(alertId, resolvedBy = 'system') {
    const alert = this.alerts.get(alertId);
    if (!alert) return;

    alert.resolved = true;
    alert.resolvedAt = new Date();
    alert.resolvedBy = resolvedBy;

    this.alerts.set(alertId, alert);

    await this.auditService.log(
      null,
      'monitoring_alert_resolved',
      { alertId, resolvedBy },
      'info'
    );

    this.emit('alertResolved', alert);
  }

  registerService(name, config) {
    this.services.set(name, {
      ...config,
      registered: new Date(),
      healthy: null,
      lastChecked: null
    });

    logger.info(`Service registered for monitoring: ${name}`);
  }

  unregisterService(name) {
    this.services.delete(name);
    logger.info(`Service unregistered from monitoring: ${name}`);
  }

  getLatestSystemMetrics() {
    const systemMetrics = Array.from(this.realTimeMetrics.entries())
      .filter(([key]) => key.startsWith('system_'))
      .sort(([a], [b]) => b.localeCompare(a));

    return systemMetrics.length > 0 ? systemMetrics[0][1] : null;
  }

  getSystemMetricsHistory(hours = 24) {
    const cutoff = Date.now() - (hours * 60 * 60 * 1000);

    return Array.from(this.realTimeMetrics.entries())
      .filter(([key, metrics]) =>
        key.startsWith('system_') && metrics.timestamp.getTime() > cutoff
      )
      .map(([, metrics]) => metrics)
      .sort((a, b) => a.timestamp - b.timestamp);
  }

  getActiveAlerts() {
    return Array.from(this.alerts.values())
      .filter(alert => !alert.resolved)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  getServiceStatuses() {
    return Array.from(this.services.entries()).map(([name, config]) => ({
      name,
      ...config
    }));
  }

  cleanupOldMetrics() {
    const cutoff = Date.now() - (this.config.retention.metrics * 24 * 60 * 60 * 1000);

    for (const [key, metrics] of this.realTimeMetrics) {
      if (metrics.timestamp.getTime() < cutoff) {
        this.realTimeMetrics.delete(key);
      }
    }
  }

  cleanupOldAlerts() {
    const cutoff = Date.now() - (this.config.retention.alerts * 24 * 60 * 60 * 1000);

    for (const [alertId, alert] of this.alerts) {
      if (alert.timestamp.getTime() < cutoff) {
        this.alerts.delete(alertId);
      }
    }
  }

  async getMonitoringDashboard() {
    return {
      system: this.getLatestSystemMetrics(),
      services: this.getServiceStatuses(),
      alerts: this.getActiveAlerts(),
      metrics: {
        system: this.getSystemMetricsHistory(1), // Last hour
        network: Array.from(this.realTimeMetrics.entries())
          .filter(([key]) => key.startsWith('network_'))
          .slice(-60) // Last 60 network measurements
          .map(([, metrics]) => metrics)
      },
      status: {
        monitoring: this.isMonitoring,
        uptime: process.uptime(),
        version: process.env.APP_VERSION || '1.0.0'
      }
    };
  }
}