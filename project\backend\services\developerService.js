import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import { NotificationService } from './notificationService.js';
import { MonitoringService } from './monitoringService.js';
import { BackupService } from './backupService.js';
import { Project } from '../models/Project.js';
import { Deployment } from '../models/Deployment.js';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import crypto from 'crypto';

const execAsync = promisify(exec);

export class DeveloperService {
  constructor(config = {}) {
    this.config = {
      projectsPath: config.projectsPath || '/var/www/projects',
      maxProjects: config.maxProjects || 10,
      defaultStack: config.defaultStack || 'Node.js',
      supportedStacks: config.supportedStacks || [
        'LAMP', 'LEMP', 'MEAN', 'MERN', 'MEVN', 'Django', 'Flask',
        'Node.js', 'React', 'Vue.js', 'Angular', 'Laravel', 'WordPress',
        'Next.js', 'Nuxt.js', 'Gatsby', 'Svelte', 'FastAPI', 'Spring Boot'
      ],
      templates: config.templates || {
        'wordpress': { stack: 'LAMP', files: 'wordpress-template' },
        'laravel': { stack: 'LAMP', files: 'laravel-template' },
        'react': { stack: 'Node.js', files: 'react-template' },
        'vue': { stack: 'Node.js', files: 'vue-template' },
        'next': { stack: 'Node.js', files: 'nextjs-template' }
      },
      aiFeatures: {
        debugger: config.aiFeatures?.debugger || true,
        codeAssistant: config.aiFeatures?.codeAssistant || true,
        logAnalyzer: config.aiFeatures?.logAnalyzer || true,
        deploymentOptimizer: config.aiFeatures?.deploymentOptimizer || true
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.notificationService = new NotificationService();
    this.monitoringService = new MonitoringService();
    this.backupService = new BackupService();

    // In-memory caches
    this.activeDeployments = new Map();
    this.projectMetrics = new Map();
    this.buildQueue = [];

    this.initializeDeveloperService();
  }

  async initializeDeveloperService() {
    try {
      // Ensure projects directory exists
      if (!fs.existsSync(this.config.projectsPath)) {
        fs.mkdirSync(this.config.projectsPath, { recursive: true });
      }

      // Start deployment queue processor
      this.startDeploymentProcessor();

      logger.info('Developer service initialized successfully');
    } catch (error) {
      logger.error('Error initializing developer service:', error);
    }
  }

  // ===== PROJECT MANAGEMENT =====

  async createProject(userId, projectData) {
    try {
      // Validate user project limits
      const userProjects = await Project.getByUser(userId);
      if (userProjects.length >= this.config.maxProjects) {
        throw new Error(`Maximum project limit (${this.config.maxProjects}) reached`);
      }

      // Validate stack
      if (!this.config.supportedStacks.includes(projectData.stack)) {
        throw new Error(`Unsupported stack: ${projectData.stack}`);
      }

      const project = new Project({
        name: projectData.name,
        description: projectData.description,
        owner: userId,
        stack: projectData.stack,
        template: projectData.template || 'blank',
        environments: [
          { name: 'development', branch: 'develop' },
          { name: 'staging', branch: 'staging' },
          { name: 'production', branch: 'main' }
        ]
      });

      await project.save();

      // Create project directory structure
      await this.createProjectStructure(project);

      // Initialize template if specified
      if (projectData.template && projectData.template !== 'blank') {
        await this.initializeTemplate(project, projectData.template);
      }

      // Setup default monitoring
      await this.setupProjectMonitoring(project);

      await this.auditService.log(
        userId,
        'project_created',
        { projectId: project._id, name: project.name, stack: project.stack },
        'info'
      );

      logger.info(`Project created: ${project.name} (${project._id})`);
      return project;
    } catch (error) {
      logger.error('Error creating project:', error);
      throw error;
    }
  }

  async updateProject(userId, projectId, updateData) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'manage_settings')) {
        throw new Error('Insufficient permissions to update project');
      }

      // Update allowed fields
      const allowedFields = ['name', 'description', 'environments', 'domains', 'settings'];
      const updates = {};

      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          updates[field] = updateData[field];
        }
      }

      Object.assign(project, updates);
      await project.save();

      await this.auditService.log(
        userId,
        'project_updated',
        { projectId, updates: Object.keys(updates) },
        'info'
      );

      return project;
    } catch (error) {
      logger.error('Error updating project:', error);
      throw error;
    }
  }

  async deleteProject(userId, projectId) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.owner.equals(userId)) {
        throw new Error('Only project owner can delete the project');
      }

      // Archive project instead of hard delete
      project.status = 'archived';
      await project.save();

      // Cleanup project resources
      await this.cleanupProjectResources(project);

      await this.auditService.log(
        userId,
        'project_deleted',
        { projectId, name: project.name },
        'warning'
      );

      logger.info(`Project archived: ${project.name} (${projectId})`);
      return { success: true };
    } catch (error) {
      logger.error('Error deleting project:', error);
      throw error;
    }
  }

  async cloneProject(userId, sourceProjectId, newProjectData) {
    try {
      const sourceProject = await Project.findById(sourceProjectId);
      if (!sourceProject) {
        throw new Error('Source project not found');
      }

      if (!sourceProject.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions to clone project');
      }

      // Create new project with cloned data
      const clonedProject = new Project({
        ...sourceProject.toObject(),
        _id: undefined,
        name: newProjectData.name,
        slug: undefined, // Will be auto-generated
        owner: userId,
        collaborators: [], // Start with empty collaborators
        status: 'active',
        createdAt: undefined,
        updatedAt: undefined
      });

      await clonedProject.save();

      // Clone project files
      await this.cloneProjectFiles(sourceProject, clonedProject);

      await this.auditService.log(
        userId,
        'project_cloned',
        { sourceProjectId, newProjectId: clonedProject._id, name: clonedProject.name },
        'info'
      );

      return clonedProject;
    } catch (error) {
      logger.error('Error cloning project:', error);
      throw error;
    }
  }

  // ===== DEPLOYMENT MANAGEMENT =====

  async createDeployment(userId, projectId, deploymentData) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'deploy')) {
        throw new Error('Insufficient permissions to deploy');
      }

      const deployment = new Deployment({
        project: projectId,
        environment: deploymentData.environment,
        trigger: {
          type: 'manual',
          user: userId,
          metadata: deploymentData.metadata
        },
        source: deploymentData.source,
        configuration: {
          environmentVariables: deploymentData.environmentVariables,
          buildArgs: deploymentData.buildArgs,
          resources: deploymentData.resources
        }
      });

      await deployment.save();

      // Add to deployment queue
      this.buildQueue.push(deployment._id);

      await this.auditService.log(
        userId,
        'deployment_created',
        { projectId, deploymentId: deployment._id, environment: deployment.environment },
        'info'
      );

      logger.info(`Deployment created: ${deployment.id} for project ${project.name}`);
      return deployment;
    } catch (error) {
      logger.error('Error creating deployment:', error);
      throw error;
    }
  }

  async getDeployments(userId, projectId, filters = {}) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions to view deployments');
      }

      const deployments = await Deployment.getByProject(
        projectId,
        filters.environment,
        filters.limit || 50
      );

      return deployments;
    } catch (error) {
      logger.error('Error getting deployments:', error);
      throw error;
    }
  }

  async rollbackDeployment(userId, deploymentId, reason) {
    try {
      const deployment = await Deployment.findById(deploymentId).populate('project');
      if (!deployment) {
        throw new Error('Deployment not found');
      }

      if (!deployment.project.canUserAccess(userId, 'deploy')) {
        throw new Error('Insufficient permissions to rollback deployment');
      }

      if (!deployment.canRollback()) {
        throw new Error('Deployment cannot be rolled back');
      }

      await deployment.triggerRollback(userId, reason);

      // Create rollback deployment
      const rollbackDeployment = await this.createRollbackDeployment(deployment);

      await this.auditService.log(
        userId,
        'deployment_rollback',
        { deploymentId, rollbackDeploymentId: rollbackDeployment._id, reason },
        'warning'
      );

      return rollbackDeployment;
    } catch (error) {
      logger.error('Error rolling back deployment:', error);
      throw error;
    }
  }

  // ===== FILE MANAGEMENT =====

  async getProjectFiles(userId, projectId, path = '/') {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions to view files');
      }

      const projectPath = this.getProjectPath(project);
      const fullPath = this.sanitizePath(projectPath, path);

      const files = await this.listFiles(fullPath);
      return files;
    } catch (error) {
      logger.error('Error getting project files:', error);
      throw error;
    }
  }

  async uploadFile(userId, projectId, filePath, fileContent) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'write')) {
        throw new Error('Insufficient permissions to upload files');
      }

      const projectPath = this.getProjectPath(project);
      const fullPath = this.sanitizePath(projectPath, filePath);

      // Ensure directory exists
      const dir = path.dirname(fullPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(fullPath, fileContent);

      await this.auditService.log(
        userId,
        'file_uploaded',
        { projectId, filePath },
        'info'
      );

      return { success: true, path: filePath };
    } catch (error) {
      logger.error('Error uploading file:', error);
      throw error;
    }
  }

  async deleteFile(userId, projectId, filePath) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'write')) {
        throw new Error('Insufficient permissions to delete files');
      }

      const projectPath = this.getProjectPath(project);
      const fullPath = this.sanitizePath(projectPath, filePath);

      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }

      await this.auditService.log(
        userId,
        'file_deleted',
        { projectId, filePath },
        'info'
      );

      return { success: true };
    } catch (error) {
      logger.error('Error deleting file:', error);
      throw error;
    }
  }

  // ===== DATABASE MANAGEMENT =====

  async createDatabase(userId, projectId, databaseConfig) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'manage_settings')) {
        throw new Error('Insufficient permissions to create database');
      }

      const database = {
        name: databaseConfig.name,
        type: databaseConfig.type,
        version: databaseConfig.version || 'latest',
        username: this.generateDatabaseUsername(project, databaseConfig.name),
        password: this.generateSecurePassword(),
        host: 'localhost',
        port: this.getDefaultPort(databaseConfig.type)
      };

      // Create actual database
      await this.provisionDatabase(database);

      project.databases.push(database);
      await project.save();

      await this.auditService.log(
        userId,
        'database_created',
        { projectId, databaseName: database.name, type: database.type },
        'info'
      );

      return database;
    } catch (error) {
      logger.error('Error creating database:', error);
      throw error;
    }
  }

  async getDatabaseAccess(userId, projectId, databaseName) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions to access database');
      }

      const database = project.databases.find(db => db.name === databaseName);
      if (!database) {
        throw new Error('Database not found');
      }

      // Generate secure access token
      const accessToken = this.generateDatabaseAccessToken(userId, projectId, databaseName);

      return {
        connectionString: this.buildConnectionString(database),
        accessToken,
        adminUrl: this.getDatabaseAdminUrl(database),
        expiresAt: new Date(Date.now() + 3600000) // 1 hour
      };
    } catch (error) {
      logger.error('Error getting database access:', error);
      throw error;
    }
  }

  // ===== AI-POWERED TOOLS =====

  async analyzeError(userId, projectId, errorData) {
    try {
      if (!this.config.aiFeatures.debugger) {
        throw new Error('AI debugger is not enabled');
      }

      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions');
      }

      // AI error analysis (mock implementation)
      const analysis = {
        errorType: this.classifyError(errorData.message),
        possibleCauses: this.identifyPossibleCauses(errorData),
        recommendations: this.generateRecommendations(errorData),
        codeSnippets: this.suggestCodeFixes(errorData),
        relatedDocumentation: this.findRelatedDocs(errorData, project.stack)
      };

      await this.auditService.log(
        userId,
        'ai_error_analysis',
        { projectId, errorType: analysis.errorType },
        'info'
      );

      return analysis;
    } catch (error) {
      logger.error('Error analyzing error:', error);
      throw error;
    }
  }

  async generateCode(userId, projectId, codeRequest) {
    try {
      if (!this.config.aiFeatures.codeAssistant) {
        throw new Error('AI code assistant is not enabled');
      }

      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'write')) {
        throw new Error('Insufficient permissions');
      }

      // AI code generation (mock implementation)
      const generatedCode = {
        code: this.generateCodeSnippet(codeRequest, project.stack),
        explanation: this.explainCode(codeRequest),
        alternatives: this.suggestAlternatives(codeRequest),
        bestPractices: this.getBestPractices(codeRequest, project.stack)
      };

      await this.auditService.log(
        userId,
        'ai_code_generation',
        { projectId, requestType: codeRequest.type },
        'info'
      );

      return generatedCode;
    } catch (error) {
      logger.error('Error generating code:', error);
      throw error;
    }
  }

  async optimizeDeployment(userId, projectId, deploymentConfig) {
    try {
      if (!this.config.aiFeatures.deploymentOptimizer) {
        throw new Error('AI deployment optimizer is not enabled');
      }

      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'deploy')) {
        throw new Error('Insufficient permissions');
      }

      // AI deployment optimization (mock implementation)
      const optimization = {
        recommendedStrategy: this.recommendDeploymentStrategy(project, deploymentConfig),
        resourceOptimization: this.optimizeResources(project, deploymentConfig),
        performanceImprovements: this.suggestPerformanceImprovements(project),
        securityRecommendations: this.getSecurityRecommendations(project),
        costOptimization: this.optimizeCosts(project, deploymentConfig)
      };

      return optimization;
    } catch (error) {
      logger.error('Error optimizing deployment:', error);
      throw error;
    }
  }

  // ===== MONITORING AND ANALYTICS =====

  async getProjectMetrics(userId, projectId, timeRange = '24h') {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.canUserAccess(userId, 'read')) {
        throw new Error('Insufficient permissions');
      }

      const metrics = await this.collectProjectMetrics(project, timeRange);
      return metrics;
    } catch (error) {
      logger.error('Error getting project metrics:', error);
      throw error;
    }
  }

  async getDashboardData(userId) {
    try {
      const projects = await Project.getByUser(userId);
      const activeDeployments = await Deployment.find({
        project: { $in: projects.map(p => p._id) },
        status: { $in: ['pending', 'building', 'deploying'] }
      });

      const dashboardData = {
        projects: {
          total: projects.length,
          active: projects.filter(p => p.status === 'active').length,
          recentActivity: projects.slice(0, 5)
        },
        deployments: {
          active: activeDeployments.length,
          recent: await this.getRecentDeployments(userId, 10)
        },
        resources: await this.getUserResourceUsage(userId),
        alerts: await this.getUserAlerts(userId)
      };

      return dashboardData;
    } catch (error) {
      logger.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  // ===== HELPER METHODS =====

  async createProjectStructure(project) {
    const projectPath = this.getProjectPath(project);

    // Create basic directory structure
    const directories = [
      'src',
      'public',
      'config',
      'logs',
      'backups'
    ];

    for (const dir of directories) {
      const dirPath = path.join(projectPath, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    }

    // Create basic files
    const files = {
      'README.md': `# ${project.name}\n\n${project.description || 'Project description'}`,
      '.gitignore': this.getGitignoreTemplate(project.stack),
      'package.json': this.getPackageJsonTemplate(project)
    };

    for (const [filename, content] of Object.entries(files)) {
      const filePath = path.join(projectPath, filename);
      fs.writeFileSync(filePath, content);
    }
  }

  getProjectPath(project) {
    return path.join(this.config.projectsPath, project.slug);
  }

  sanitizePath(basePath, userPath) {
    const resolvedPath = path.resolve(basePath, userPath);
    if (!resolvedPath.startsWith(basePath)) {
      throw new Error('Invalid path');
    }
    return resolvedPath;
  }

  generateSecurePassword(length = 16) {
    return crypto.randomBytes(length).toString('base64').slice(0, length);
  }

  generateDatabaseUsername(project, databaseName) {
    return `${project.slug}_${databaseName}`.substring(0, 16);
  }

  getDefaultPort(databaseType) {
    const ports = {
      mysql: 3306,
      postgresql: 5432,
      mongodb: 27017,
      redis: 6379
    };
    return ports[databaseType] || 3306;
  }

  startDeploymentProcessor() {
    setInterval(async () => {
      if (this.buildQueue.length > 0) {
        const deploymentId = this.buildQueue.shift();
        await this.processDeployment(deploymentId);
      }
    }, 5000); // Check every 5 seconds
  }

  async processDeployment(deploymentId) {
    try {
      const deployment = await Deployment.findById(deploymentId);
      if (!deployment) return;

      await deployment.updateStatus('building');

      // Mock deployment process
      setTimeout(async () => {
        await deployment.updateStatus('deploying');

        setTimeout(async () => {
          await deployment.updateStatus('success');
          this.activeDeployments.delete(deploymentId);
        }, 10000); // 10 seconds deploy time
      }, 15000); // 15 seconds build time

    } catch (error) {
      logger.error('Error processing deployment:', error);
    }
  }

  // ===== AI HELPER METHODS =====

  classifyError(errorMessage) {
    const errorTypes = {
      'syntax': /syntax error|unexpected token|parse error/i,
      'runtime': /runtime error|null pointer|undefined/i,
      'network': /network error|connection refused|timeout/i,
      'database': /database error|sql error|connection failed/i,
      'permission': /permission denied|access denied|unauthorized/i
    };

    for (const [type, pattern] of Object.entries(errorTypes)) {
      if (pattern.test(errorMessage)) {
        return type;
      }
    }
    return 'unknown';
  }

  identifyPossibleCauses(errorData) {
    const causes = [];
    const errorType = this.classifyError(errorData.message);

    switch (errorType) {
      case 'syntax':
        causes.push('Missing semicolon or bracket', 'Typo in variable name', 'Incorrect function syntax');
        break;
      case 'runtime':
        causes.push('Null or undefined variable', 'Type mismatch', 'Missing dependency');
        break;
      case 'network':
        causes.push('Server is down', 'Incorrect URL', 'Firewall blocking request');
        break;
      case 'database':
        causes.push('Database connection failed', 'Invalid query', 'Missing table or column');
        break;
      default:
        causes.push('Check logs for more details', 'Verify configuration', 'Review recent changes');
    }

    return causes;
  }

  generateRecommendations(errorData) {
    const recommendations = [];
    const errorType = this.classifyError(errorData.message);

    switch (errorType) {
      case 'syntax':
        recommendations.push(
          'Use a code linter to catch syntax errors',
          'Enable syntax highlighting in your editor',
          'Review the code around line ' + (errorData.line || 'mentioned in error')
        );
        break;
      case 'runtime':
        recommendations.push(
          'Add null checks before using variables',
          'Use try-catch blocks for error handling',
          'Verify all dependencies are installed'
        );
        break;
      case 'network':
        recommendations.push(
          'Check network connectivity',
          'Verify API endpoints are correct',
          'Implement retry logic with exponential backoff'
        );
        break;
      case 'database':
        recommendations.push(
          'Check database connection settings',
          'Verify database schema matches your queries',
          'Use connection pooling for better reliability'
        );
        break;
    }

    return recommendations;
  }

  suggestCodeFixes(errorData) {
    const fixes = [];
    const errorType = this.classifyError(errorData.message);

    switch (errorType) {
      case 'syntax':
        fixes.push({
          description: 'Add missing semicolon',
          code: 'const variable = value; // Add semicolon at the end'
        });
        break;
      case 'runtime':
        fixes.push({
          description: 'Add null check',
          code: 'if (variable !== null && variable !== undefined) {\n  // Your code here\n}'
        });
        break;
      case 'network':
        fixes.push({
          description: 'Add error handling for network requests',
          code: 'try {\n  const response = await fetch(url);\n  const data = await response.json();\n} catch (error) {\n  console.error("Network error:", error);\n}'
        });
        break;
    }

    return fixes;
  }

  findRelatedDocs(errorData, stack) {
    const docs = {
      'Node.js': [
        'https://nodejs.org/api/',
        'https://developer.mozilla.org/en-US/docs/Web/JavaScript'
      ],
      'React': [
        'https://reactjs.org/docs/',
        'https://create-react-app.dev/docs/'
      ],
      'Vue.js': [
        'https://vuejs.org/guide/',
        'https://cli.vuejs.org/guide/'
      ],
      'Laravel': [
        'https://laravel.com/docs/',
        'https://laracasts.com/'
      ]
    };

    return docs[stack] || ['https://stackoverflow.com/', 'https://developer.mozilla.org/'];
  }

  generateCodeSnippet(codeRequest, stack) {
    const snippets = {
      'api_endpoint': {
        'Node.js': 'app.get("/api/endpoint", (req, res) => {\n  res.json({ message: "Hello World" });\n});',
        'Laravel': 'Route::get("/api/endpoint", function () {\n    return response()->json(["message" => "Hello World"]);\n});'
      },
      'database_query': {
        'Node.js': 'const users = await User.find({ active: true });',
        'Laravel': '$users = User::where("active", true)->get();'
      }
    };

    return snippets[codeRequest.type]?.[stack] || '// Code snippet not available for this stack';
  }

  explainCode(codeRequest) {
    return `This code snippet creates a ${codeRequest.type} that ${codeRequest.description || 'performs the requested functionality'}.`;
  }

  suggestAlternatives(codeRequest) {
    return [
      'Consider using async/await for better readability',
      'Add error handling with try-catch blocks',
      'Use TypeScript for better type safety'
    ];
  }

  getBestPractices(codeRequest, stack) {
    const practices = {
      'Node.js': [
        'Use environment variables for configuration',
        'Implement proper error handling',
        'Use middleware for common functionality'
      ],
      'React': [
        'Use functional components with hooks',
        'Implement proper state management',
        'Optimize re-renders with useMemo and useCallback'
      ],
      'Laravel': [
        'Use Eloquent ORM for database operations',
        'Implement proper validation',
        'Use middleware for authentication'
      ]
    };

    return practices[stack] || ['Follow coding standards', 'Write tests', 'Document your code'];
  }

  recommendDeploymentStrategy(project, deploymentConfig) {
    if (project.environments.length > 1) {
      return 'blue_green';
    }
    return 'rolling';
  }

  optimizeResources(project, deploymentConfig) {
    return {
      cpu: Math.max(0.5, project.resources.usage.cpu.average * 1.2),
      memory: Math.max(256, project.resources.usage.memory.average * 1.3),
      replicas: project.environments.find(e => e.name === 'production') ? 2 : 1
    };
  }

  suggestPerformanceImprovements(project) {
    return [
      'Enable gzip compression',
      'Implement caching strategy',
      'Optimize database queries',
      'Use CDN for static assets'
    ];
  }

  getSecurityRecommendations(project) {
    return [
      'Enable HTTPS with SSL certificates',
      'Implement rate limiting',
      'Use environment variables for secrets',
      'Enable firewall rules'
    ];
  }

  optimizeCosts(project, deploymentConfig) {
    return {
      estimatedMonthlyCost: '$25-50',
      recommendations: [
        'Use auto-scaling to reduce idle costs',
        'Implement efficient caching',
        'Optimize database queries'
      ]
    };
  }

  // ===== UTILITY METHODS =====

  async listFiles(directoryPath) {
    try {
      const items = fs.readdirSync(directoryPath, { withFileTypes: true });
      return items.map(item => ({
        name: item.name,
        type: item.isDirectory() ? 'directory' : 'file',
        size: item.isFile() ? fs.statSync(path.join(directoryPath, item.name)).size : null,
        modified: fs.statSync(path.join(directoryPath, item.name)).mtime
      }));
    } catch (error) {
      logger.error('Error listing files:', error);
      return [];
    }
  }

  async initializeTemplate(project, templateName) {
    const template = this.config.templates[templateName];
    if (!template) {
      logger.warn(`Template ${templateName} not found`);
      return;
    }

    // Copy template files to project directory
    const templatePath = path.join(__dirname, '../templates', template.files);
    const projectPath = this.getProjectPath(project);

    if (fs.existsSync(templatePath)) {
      await this.copyDirectory(templatePath, projectPath);
    }
  }

  async copyDirectory(src, dest) {
    const { stdout } = await execAsync(`cp -r ${src}/* ${dest}/`);
    return stdout;
  }

  async setupProjectMonitoring(project) {
    // Register project with monitoring service
    await this.monitoringService.registerService(
      `project_${project._id}`,
      {
        type: 'http',
        url: project.primaryDomain || `http://localhost:3000`,
        healthCheck: '/health'
      }
    );
  }

  async cleanupProjectResources(project) {
    // Cleanup project files, databases, monitoring, etc.
    const projectPath = this.getProjectPath(project);

    if (fs.existsSync(projectPath)) {
      // Move to archive instead of deleting
      const archivePath = path.join(this.config.projectsPath, 'archived', project.slug);
      fs.renameSync(projectPath, archivePath);
    }

    // Cleanup databases
    for (const database of project.databases) {
      await this.cleanupDatabase(database);
    }

    // Unregister from monitoring
    await this.monitoringService.unregisterService(`project_${project._id}`);
  }

  async cloneProjectFiles(sourceProject, targetProject) {
    const sourcePath = this.getProjectPath(sourceProject);
    const targetPath = this.getProjectPath(targetProject);

    if (fs.existsSync(sourcePath)) {
      await this.copyDirectory(sourcePath, targetPath);
    }
  }

  async provisionDatabase(database) {
    // Mock database provisioning
    logger.info(`Provisioning ${database.type} database: ${database.name}`);
    // In real implementation, this would create actual database
  }

  async cleanupDatabase(database) {
    // Mock database cleanup
    logger.info(`Cleaning up database: ${database.name}`);
    // In real implementation, this would drop the database
  }

  buildConnectionString(database) {
    switch (database.type) {
      case 'mysql':
        return `mysql://${database.username}:${database.password}@${database.host}:${database.port}/${database.name}`;
      case 'postgresql':
        return `postgresql://${database.username}:${database.password}@${database.host}:${database.port}/${database.name}`;
      case 'mongodb':
        return `mongodb://${database.username}:${database.password}@${database.host}:${database.port}/${database.name}`;
      default:
        return '';
    }
  }

  getDatabaseAdminUrl(database) {
    const adminUrls = {
      mysql: '/phpmyadmin',
      postgresql: '/adminer',
      mongodb: '/mongo-express'
    };
    return adminUrls[database.type] || '/admin';
  }

  generateDatabaseAccessToken(userId, projectId, databaseName) {
    const payload = { userId, projectId, databaseName, exp: Date.now() + 3600000 };
    return Buffer.from(JSON.stringify(payload)).toString('base64');
  }

  async createRollbackDeployment(originalDeployment) {
    const rollbackDeployment = new Deployment({
      project: originalDeployment.project,
      environment: originalDeployment.environment,
      trigger: {
        type: 'rollback',
        user: originalDeployment.rollback.triggeredBy,
        source: 'rollback',
        metadata: { originalDeployment: originalDeployment._id }
      },
      source: originalDeployment.rollback.previousDeployment.source,
      configuration: originalDeployment.rollback.previousDeployment.configuration
    });

    await rollbackDeployment.save();
    this.buildQueue.push(rollbackDeployment._id);

    return rollbackDeployment;
  }

  async collectProjectMetrics(project, timeRange) {
    // Mock metrics collection
    return {
      uptime: 99.9,
      responseTime: 150,
      requests: 1250,
      errors: 5,
      cpu: 45,
      memory: 60,
      storage: 30
    };
  }

  async getRecentDeployments(userId, limit) {
    const projects = await Project.getByUser(userId);
    return await Deployment.find({
      project: { $in: projects.map(p => p._id) }
    }).sort({ createdAt: -1 }).limit(limit);
  }

  async getUserResourceUsage(userId) {
    const projects = await Project.getByUser(userId);

    let totalCPU = 0, totalMemory = 0, totalStorage = 0;

    for (const project of projects) {
      totalCPU += project.resources.usage.cpu.current || 0;
      totalMemory += project.resources.usage.memory.current || 0;
      totalStorage += project.resources.usage.storage.used || 0;
    }

    return {
      cpu: { used: totalCPU, limit: 100 },
      memory: { used: totalMemory, limit: 2048 },
      storage: { used: totalStorage, limit: 10240 }
    };
  }

  async getUserAlerts(userId) {
    // Mock alerts
    return [
      {
        type: 'deployment_failed',
        message: 'Deployment failed for project MyApp',
        severity: 'error',
        timestamp: new Date()
      }
    ];
  }

  getGitignoreTemplate(stack) {
    const templates = {
      'Node.js': 'node_modules/\n.env\n*.log\ndist/\nbuild/',
      'Laravel': 'vendor/\n.env\nstorage/logs/\npublic/storage',
      'React': 'node_modules/\nbuild/\n.env.local\n*.log',
      'Vue.js': 'node_modules/\ndist/\n.env.local\n*.log'
    };
    return templates[stack] || 'node_modules/\n.env\n*.log';
  }

  getPackageJsonTemplate(project) {
    return JSON.stringify({
      name: project.slug,
      version: '1.0.0',
      description: project.description || '',
      main: 'index.js',
      scripts: {
        start: 'node index.js',
        dev: 'nodemon index.js',
        test: 'jest'
      },
      dependencies: {},
      devDependencies: {}
    }, null, 2);
  }
}
