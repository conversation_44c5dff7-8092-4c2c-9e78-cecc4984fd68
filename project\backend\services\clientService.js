import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import { NotificationService } from './notificationService.js';
import { BillingService } from './billingService.js';
import { BackupService } from './backupService.js';
import { MonitoringService } from './monitoringService.js';
import { Client } from '../models/Client.js';
import { SupportTicket } from '../models/SupportTicket.js';
import crypto from 'crypto';

export class ClientService {
  constructor(config = {}) {
    this.config = {
      defaultPlan: config.defaultPlan || 'basic',
      trialDuration: config.trialDuration || 14, // days
      maxWebsites: config.maxWebsites || {
        basic: 1,
        professional: 5,
        enterprise: 25
      },
      storageQuotas: config.storageQuotas || {
        basic: 1, // GB
        professional: 10,
        enterprise: 100
      },
      features: config.features || {
        basic: ['websites', 'email', 'support', 'ssl'],
        professional: ['websites', 'email', 'support', 'ssl', 'analytics', 'backups', 'cdn'],
        enterprise: ['websites', 'email', 'support', 'ssl', 'analytics', 'backups', 'cdn', 'advanced_security', 'api_access', 'white_label']
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.notificationService = new NotificationService();
    this.billingService = new BillingService();
    this.backupService = new BackupService();
    this.monitoringService = new MonitoringService();
    
    this.initializeClientService();
  }

  async initializeClientService() {
    try {
      logger.info('Client service initialized successfully');
    } catch (error) {
      logger.error('Error initializing client service:', error);
    }
  }

  // ===== CLIENT MANAGEMENT =====

  async createClient(userId, clientData) {
    try {
      const client = new Client({
        user: userId,
        profile: clientData.profile || {},
        subscription: {
          plan: clientData.plan || this.config.defaultPlan,
          status: 'trial',
          startDate: new Date(),
          endDate: new Date(Date.now() + this.config.trialDuration * 24 * 60 * 60 * 1000),
          billingCycle: clientData.billingCycle || 'monthly'
        },
        resources: {
          allocated: this.getPlanResources(clientData.plan || this.config.defaultPlan),
          usage: {
            storage: { used: 0, files: 0, databases: 0, emails: 0 },
            bandwidth: { used: 0, incoming: 0, outgoing: 0 },
            domains: { used: 0, active: 0 },
            emailAccounts: { used: 0, active: 0 },
            databases: { used: 0, active: 0 }
          },
          limits: this.getPlanLimits(clientData.plan || this.config.defaultPlan)
        },
        managedBy: {
          developer: clientData.managedBy?.developer,
          reseller: clientData.managedBy?.reseller,
          agency: clientData.managedBy?.agency
        }
      });

      await client.save();

      // Send welcome notification
      await this.sendWelcomeNotification(client);

      await this.auditService.log(
        userId,
        'client_created',
        { clientId: client._id, plan: client.subscription.plan },
        'info'
      );

      logger.info(`Client created: ${client._id} for user ${userId}`);
      return client;
    } catch (error) {
      logger.error('Error creating client:', error);
      throw error;
    }
  }

  async getClientProfile(userId) {
    try {
      const client = await Client.findOne({ user: userId })
        .populate('user', 'email firstName lastName')
        .populate('managedBy.developer', 'firstName lastName email')
        .populate('managedBy.reseller', 'firstName lastName email');

      if (!client) {
        throw new Error('Client profile not found');
      }

      return client;
    } catch (error) {
      logger.error('Error getting client profile:', error);
      throw error;
    }
  }

  async updateClientProfile(userId, updateData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      // Update allowed fields
      const allowedFields = ['profile', 'notifications', 'settings'];
      const updates = {};
      
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          updates[field] = { ...client[field], ...updateData[field] };
        }
      }

      Object.assign(client, updates);
      await client.save();

      await this.auditService.log(
        userId,
        'client_profile_updated',
        { clientId: client._id, updates: Object.keys(updates) },
        'info'
      );

      return client;
    } catch (error) {
      logger.error('Error updating client profile:', error);
      throw error;
    }
  }

  // ===== WEBSITE MANAGEMENT =====

  async getWebsites(userId) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      // Enrich websites with real-time data
      const enrichedWebsites = await Promise.all(
        client.websites.map(async (website) => {
          const uptime = await this.getWebsiteUptime(website.domain);
          const sslStatus = await this.getSSLStatus(website.domain);
          const securityScan = await this.getLatestSecurityScan(website.domain);
          
          return {
            ...website.toObject(),
            uptime,
            sslStatus,
            securityScan,
            lastChecked: new Date()
          };
        })
      );

      return enrichedWebsites;
    } catch (error) {
      logger.error('Error getting websites:', error);
      throw error;
    }
  }

  async addWebsite(userId, websiteData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      // Check website limits
      const maxWebsites = this.config.maxWebsites[client.subscription.plan];
      if (client.websites.length >= maxWebsites) {
        throw new Error(`Maximum website limit (${maxWebsites}) reached for ${client.subscription.plan} plan`);
      }

      const website = {
        name: websiteData.name,
        domain: websiteData.domain,
        subdomain: websiteData.subdomain,
        platform: websiteData.platform || 'custom',
        techStack: websiteData.techStack || 'html',
        ssl: {
          enabled: false,
          autoRenew: true
        },
        backups: {
          enabled: true,
          frequency: 'daily',
          retentionDays: 30
        },
        analytics: {
          enabled: false
        },
        security: {
          firewall: true,
          ddosProtection: true,
          malwareScanning: true,
          securityScore: 0
        },
        performance: {
          caching: true,
          compression: true,
          optimization: false
        }
      };

      await client.addWebsite(website);

      // Setup monitoring for the website
      await this.setupWebsiteMonitoring(website);

      // Generate SSL certificate if domain is provided
      if (website.domain) {
        await this.generateSSLCertificate(client._id, website.domain);
      }

      await this.auditService.log(
        userId,
        'website_added',
        { clientId: client._id, domain: website.domain },
        'info'
      );

      return website;
    } catch (error) {
      logger.error('Error adding website:', error);
      throw error;
    }
  }

  async updateWebsite(userId, websiteId, updateData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const website = client.websites.id(websiteId);
      if (!website) {
        throw new Error('Website not found');
      }

      // Update allowed fields
      const allowedFields = ['name', 'ssl', 'analytics', 'performance'];
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          Object.assign(website[field], updateData[field]);
        }
      }

      website.lastUpdated = new Date();
      await client.save();

      await this.auditService.log(
        userId,
        'website_updated',
        { clientId: client._id, websiteId, updates: Object.keys(updateData) },
        'info'
      );

      return website;
    } catch (error) {
      logger.error('Error updating website:', error);
      throw error;
    }
  }

  async deleteWebsite(userId, websiteId) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const website = client.websites.id(websiteId);
      if (!website) {
        throw new Error('Website not found');
      }

      // Cleanup website resources
      await this.cleanupWebsiteResources(website);

      client.websites.id(websiteId).remove();
      await client.save();

      await this.auditService.log(
        userId,
        'website_deleted',
        { clientId: client._id, websiteId, domain: website.domain },
        'warning'
      );

      return { success: true };
    } catch (error) {
      logger.error('Error deleting website:', error);
      throw error;
    }
  }

  // ===== ANALYTICS & REPORTING =====

  async getWebsiteAnalytics(userId, websiteId, timeRange = '30d') {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const website = client.websites.id(websiteId);
      if (!website) {
        throw new Error('Website not found');
      }

      // Mock analytics data - would integrate with actual analytics service
      const analytics = {
        traffic: {
          visitors: this.generateTimeSeriesData(timeRange, 100, 500),
          pageViews: this.generateTimeSeriesData(timeRange, 200, 1000),
          bounceRate: 45.2,
          avgSessionDuration: 180, // seconds
          topPages: [
            { page: '/', views: 1250, percentage: 35 },
            { page: '/about', views: 890, percentage: 25 },
            { page: '/contact', views: 567, percentage: 16 }
          ],
          topReferrers: [
            { source: 'google.com', visits: 890, percentage: 45 },
            { source: 'direct', visits: 567, percentage: 28 },
            { source: 'facebook.com', visits: 234, percentage: 12 }
          ]
        },
        performance: {
          uptime: 99.9,
          averageResponseTime: 245, // ms
          errors: 12,
          slowestPages: [
            { page: '/dashboard', responseTime: 890 },
            { page: '/reports', responseTime: 567 }
          ]
        },
        security: {
          threats: 5,
          blockedAttacks: 23,
          lastScan: new Date(),
          securityScore: website.security.securityScore || 85
        }
      };

      return analytics;
    } catch (error) {
      logger.error('Error getting website analytics:', error);
      throw error;
    }
  }

  async generateReport(userId, reportType, options = {}) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const report = {
        id: this.generateReportId(),
        type: reportType,
        generatedAt: new Date(),
        period: options.period || '30d',
        data: {}
      };

      switch (reportType) {
        case 'traffic':
          report.data = await this.generateTrafficReport(client, options);
          break;
        case 'performance':
          report.data = await this.generatePerformanceReport(client, options);
          break;
        case 'security':
          report.data = await this.generateSecurityReport(client, options);
          break;
        case 'billing':
          report.data = await this.generateBillingReport(client, options);
          break;
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }

      await this.auditService.log(
        userId,
        'report_generated',
        { clientId: client._id, reportType, reportId: report.id },
        'info'
      );

      return report;
    } catch (error) {
      logger.error('Error generating report:', error);
      throw error;
    }
  }

  // ===== EMAIL MANAGEMENT =====

  async getEmailAccounts(userId) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      // Remove sensitive data before returning
      const emailAccounts = client.emailAccounts.map(account => ({
        _id: account._id,
        email: account.email,
        quota: account.quota,
        used: account.used,
        forwarders: account.forwarders,
        autoResponder: account.autoResponder,
        createdAt: account.createdAt,
        lastLogin: account.lastLogin
      }));

      return emailAccounts;
    } catch (error) {
      logger.error('Error getting email accounts:', error);
      throw error;
    }
  }

  async createEmailAccount(userId, emailData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      // Check email account limits
      const maxEmails = client.resources.allocated.emailAccounts;
      if (client.emailAccounts.length >= maxEmails) {
        throw new Error(`Maximum email account limit (${maxEmails}) reached`);
      }

      const emailAccount = {
        email: emailData.email,
        password: this.encryptPassword(emailData.password),
        quota: emailData.quota || 1024, // MB
        used: 0,
        forwarders: emailData.forwarders || [],
        autoResponder: {
          enabled: false
        },
        filters: [],
        createdAt: new Date()
      };

      client.emailAccounts.push(emailAccount);
      client.resources.usage.emailAccounts.used += 1;
      client.resources.usage.emailAccounts.active += 1;

      await client.save();

      await this.auditService.log(
        userId,
        'email_account_created',
        { clientId: client._id, email: emailAccount.email },
        'info'
      );

      return emailAccount;
    } catch (error) {
      logger.error('Error creating email account:', error);
      throw error;
    }
  }

  async updateEmailAccount(userId, emailId, updateData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const emailAccount = client.emailAccounts.id(emailId);
      if (!emailAccount) {
        throw new Error('Email account not found');
      }

      // Update allowed fields
      const allowedFields = ['quota', 'forwarders', 'autoResponder', 'filters'];
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          emailAccount[field] = updateData[field];
        }
      }

      if (updateData.password) {
        emailAccount.password = this.encryptPassword(updateData.password);
      }

      await client.save();

      await this.auditService.log(
        userId,
        'email_account_updated',
        { clientId: client._id, emailId, updates: Object.keys(updateData) },
        'info'
      );

      return emailAccount;
    } catch (error) {
      logger.error('Error updating email account:', error);
      throw error;
    }
  }

  // ===== BILLING MANAGEMENT =====

  async getBillingInfo(userId) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const billingInfo = {
        subscription: client.subscription,
        billing: client.billing,
        usage: client.resources.usage,
        allocated: client.resources.allocated,
        nextBilling: this.calculateNextBillingDate(client.subscription),
        estimatedCost: await this.calculateEstimatedCost(client)
      };

      return billingInfo;
    } catch (error) {
      logger.error('Error getting billing info:', error);
      throw error;
    }
  }

  async updateSubscription(userId, planData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const oldPlan = client.subscription.plan;
      const newPlan = planData.plan;

      // Update subscription
      client.subscription.plan = newPlan;
      client.subscription.status = 'active';
      client.resources.allocated = this.getPlanResources(newPlan);

      // Process payment if upgrading
      if (this.isPlanUpgrade(oldPlan, newPlan)) {
        const paymentResult = await this.billingService.processPayment(
          client.billing.paymentMethod,
          this.calculateUpgradeCost(oldPlan, newPlan)
        );

        if (!paymentResult.success) {
          throw new Error('Payment failed: ' + paymentResult.error);
        }
      }

      await client.save();

      await this.auditService.log(
        userId,
        'subscription_updated',
        { clientId: client._id, oldPlan, newPlan },
        'info'
      );

      return client.subscription;
    } catch (error) {
      logger.error('Error updating subscription:', error);
      throw error;
    }
  }

  // ===== SUPPORT MANAGEMENT =====

  async createSupportTicket(userId, ticketData) {
    try {
      const client = await Client.findOne({ user: userId });
      if (!client) {
        throw new Error('Client not found');
      }

      const ticket = new SupportTicket({
        user: userId,
        subject: ticketData.subject,
        description: ticketData.description,
        category: ticketData.category || 'general',
        priority: ticketData.priority || 'medium',
        status: 'open',
        source: 'client_portal',
        metadata: {
          clientId: client._id,
          plan: client.subscription.plan
        }
      });

      await ticket.save();

      // Add to client's support tickets
      client.support.tickets.push({
        ticketId: ticket.ticketId,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        createdAt: ticket.createdAt
      });

      await client.save();

      // Send notification to support team
      await this.notificationService.sendNotification({
        type: 'support_ticket_created',
        recipients: ['<EMAIL>'],
        data: { ticket, client }
      });

      await this.auditService.log(
        userId,
        'support_ticket_created',
        { clientId: client._id, ticketId: ticket.ticketId },
        'info'
      );

      return ticket;
    } catch (error) {
      logger.error('Error creating support ticket:', error);
      throw error;
    }
  }

  async getSupportTickets(userId) {
    try {
      const tickets = await SupportTicket.find({ user: userId })
        .sort({ createdAt: -1 })
        .limit(50);

      return tickets;
    } catch (error) {
      logger.error('Error getting support tickets:', error);
      throw error;
    }
  }

  // ===== HELPER METHODS =====

  getPlanResources(plan) {
    const resources = {
      basic: {
        storage: 1, // GB
        bandwidth: 10, // GB
        domains: 1,
        subdomains: 5,
        emailAccounts: 5,
        databases: 1,
        ftpAccounts: 1
      },
      professional: {
        storage: 10,
        bandwidth: 100,
        domains: 5,
        subdomains: 25,
        emailAccounts: 25,
        databases: 5,
        ftpAccounts: 5
      },
      enterprise: {
        storage: 100,
        bandwidth: 1000,
        domains: 25,
        subdomains: 100,
        emailAccounts: 100,
        databases: 25,
        ftpAccounts: 25
      }
    };

    return resources[plan] || resources.basic;
  }

  getPlanLimits(plan) {
    const limits = {
      basic: {
        maxFileSize: 10, // MB
        maxDatabaseSize: 100, // MB
        maxEmailSize: 25, // MB
        concurrentConnections: 100
      },
      professional: {
        maxFileSize: 50,
        maxDatabaseSize: 1000,
        maxEmailSize: 50,
        concurrentConnections: 500
      },
      enterprise: {
        maxFileSize: 100,
        maxDatabaseSize: 10000,
        maxEmailSize: 100,
        concurrentConnections: 1000
      }
    };

    return limits[plan] || limits.basic;
  }

  async sendWelcomeNotification(client) {
    await this.notificationService.sendNotification({
      type: 'welcome',
      recipients: [client.user],
      data: {
        plan: client.subscription.plan,
        trialEndDate: client.subscription.endDate
      }
    });
  }

  generateTimeSeriesData(timeRange, min, max) {
    const days = parseInt(timeRange.replace('d', ''));
    const data = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        value: Math.floor(Math.random() * (max - min + 1)) + min
      });
    }

    return data;
  }

  generateReportId() {
    return 'RPT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
  }

  encryptPassword(password) {
    // In production, use proper encryption
    return Buffer.from(password).toString('base64');
  }

  async getWebsiteUptime(domain) {
    // Mock uptime data - would integrate with monitoring service
    return {
      percentage: 99.9,
      lastDowntime: new Date(Date.now() - 86400000), // 1 day ago
      status: 'online'
    };
  }

  async getSSLStatus(domain) {
    // Mock SSL status - would check actual SSL certificate
    return {
      valid: true,
      issuer: 'Let\'s Encrypt',
      expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      autoRenew: true
    };
  }

  async getLatestSecurityScan(domain) {
    // Mock security scan - would integrate with security scanning service
    return {
      score: 85,
      threats: 0,
      lastScan: new Date(),
      issues: []
    };
  }

  async setupWebsiteMonitoring(website) {
    // Setup monitoring for the website
    if (website.domain) {
      await this.monitoringService.registerService(
        `website_${website._id}`,
        {
          type: 'http',
          url: `https://${website.domain}`,
          interval: 300 // 5 minutes
        }
      );
    }
  }

  async generateSSLCertificate(clientId, domain) {
    // Mock SSL certificate generation
    logger.info(`Generating SSL certificate for ${domain} (client: ${clientId})`);
    // In production, integrate with Let's Encrypt or other CA
  }

  async cleanupWebsiteResources(website) {
    // Cleanup monitoring, SSL certificates, etc.
    await this.monitoringService.unregisterService(`website_${website._id}`);
  }

  calculateNextBillingDate(subscription) {
    const date = new Date(subscription.endDate);
    
    switch (subscription.billingCycle) {
      case 'monthly':
        date.setMonth(date.getMonth() + 1);
        break;
      case 'quarterly':
        date.setMonth(date.getMonth() + 3);
        break;
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1);
        break;
    }

    return date;
  }

  async calculateEstimatedCost(client) {
    // Calculate estimated monthly cost based on usage
    const baseCost = this.getPlanCost(client.subscription.plan);
    const overageCost = this.calculateOverageCost(client);
    
    return baseCost + overageCost;
  }

  getPlanCost(plan) {
    const costs = {
      basic: 9.99,
      professional: 29.99,
      enterprise: 99.99
    };
    return costs[plan] || 0;
  }

  calculateOverageCost(client) {
    // Calculate overage costs for bandwidth, storage, etc.
    let overageCost = 0;
    
    // Storage overage
    const storageUsedGB = client.totalStorageUsage / 1024;
    const storageOverage = Math.max(0, storageUsedGB - client.resources.allocated.storage);
    overageCost += storageOverage * 0.10; // $0.10 per GB

    // Bandwidth overage
    const bandwidthUsedGB = client.resources.usage.bandwidth.used / 1024;
    const bandwidthOverage = Math.max(0, bandwidthUsedGB - client.resources.allocated.bandwidth);
    overageCost += bandwidthOverage * 0.05; // $0.05 per GB

    return overageCost;
  }

  isPlanUpgrade(oldPlan, newPlan) {
    const planHierarchy = { basic: 1, professional: 2, enterprise: 3 };
    return planHierarchy[newPlan] > planHierarchy[oldPlan];
  }

  calculateUpgradeCost(oldPlan, newPlan) {
    const oldCost = this.getPlanCost(oldPlan);
    const newCost = this.getPlanCost(newPlan);
    return Math.max(0, newCost - oldCost);
  }
}
