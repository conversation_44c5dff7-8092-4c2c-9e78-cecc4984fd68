import mongoose from 'mongoose';

const knowledgeBaseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    maxlength: 200,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  excerpt: {
    type: String,
    maxlength: 500
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KnowledgeBaseCategory',
    required: true
  },
  // Keep legacy category for backward compatibility
  categoryLegacy: {
    type: String,
    enum: ['general', 'troubleshooting', 'tutorial', 'faq']
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KnowledgeBaseCategory'
  },
  tags: [String],
  status: {
    type: String,
    enum: ['draft', 'published', 'archived', 'under_review'],
    default: 'draft'
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'internal', 'customer_only'],
    default: 'public'
  },
  priority: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  featured: {
    type: Boolean,
    default: false
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  publishedAt: Date,
  attachments: [{
    filename: String,
    originalName: String,
    mimeType: String,
    size: Number,
    path: String,
    description: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    // Legacy fields for backward compatibility
    name: String,
    url: String,
    type: String
  }],
  relatedArticles: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KnowledgeBase'
  }],
  metadata: {
    difficulty: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
      default: 'beginner'
    },
    estimatedReadTime: Number, // in minutes
    language: {
      type: String,
      default: 'en'
    },
    version: {
      type: String,
      default: '1.0'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    uniqueViews: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    dislikes: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalRatings: {
      type: Number,
      default: 0
    },
    helpfulVotes: {
      type: Number,
      default: 0
    },
    notHelpfulVotes: {
      type: Number,
      default: 0
    },
    lastViewed: Date,
    popularityScore: {
      type: Number,
      default: 0
    }
  },
  // Legacy fields for backward compatibility
  views: {
    type: Number,
    default: 0
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
});

// Category schema for organizing knowledge base articles
const knowledgeBaseCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  description: String,
  icon: String,
  color: String,
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KnowledgeBaseCategory'
  },
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'internal'],
    default: 'public'
  }
}, {
  timestamps: true
});

// Enhanced indexes
knowledgeBaseSchema.index({ slug: 1 }, { unique: true });
knowledgeBaseSchema.index({ title: 'text', content: 'text', tags: 'text' });
knowledgeBaseSchema.index({ category: 1, status: 1 });
knowledgeBaseSchema.index({ status: 1, visibility: 1 });
knowledgeBaseSchema.index({ featured: 1, priority: -1 });
knowledgeBaseSchema.index({ 'analytics.views': -1 });
knowledgeBaseSchema.index({ 'analytics.popularityScore': -1 });
knowledgeBaseSchema.index({ publishedAt: -1 });
knowledgeBaseSchema.index({ tags: 1 });
knowledgeBaseSchema.index({ author: 1 });

knowledgeBaseCategorySchema.index({ slug: 1 }, { unique: true });
knowledgeBaseCategorySchema.index({ parent: 1, order: 1 });

// Virtual for URL
knowledgeBaseSchema.virtual('url').get(function() {
  return `/kb/${this.slug}`;
});

// Virtual for reading time estimation
knowledgeBaseSchema.virtual('estimatedReadTime').get(function() {
  if (this.metadata.estimatedReadTime) {
    return this.metadata.estimatedReadTime;
  }

  const wordsPerMinute = 200;
  const wordCount = this.content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
});

// Enhanced methods
knowledgeBaseSchema.methods.incrementView = function(isUnique = false) {
  this.analytics.views += 1;
  this.views += 1; // Legacy field

  if (isUnique) {
    this.analytics.uniqueViews += 1;
  }
  this.analytics.lastViewed = new Date();
  this.updatePopularityScore();
  return this.save();
};

knowledgeBaseSchema.methods.addFeedback = function(userId, rating, helpful) {
  if (rating) {
    this.updateRating(rating);
  }

  if (helpful !== undefined) {
    if (helpful) {
      this.analytics.helpfulVotes += 1;
      this.helpfulVotes += 1; // Legacy field
    } else {
      this.analytics.notHelpfulVotes += 1;
    }
  }

  this.updatePopularityScore();
  return this.save();
};

knowledgeBaseSchema.methods.updateRating = function(newRating) {
  const currentTotal = this.analytics.averageRating * this.analytics.totalRatings;
  this.analytics.totalRatings += 1;
  this.analytics.averageRating = (currentTotal + newRating) / this.analytics.totalRatings;
};

knowledgeBaseSchema.methods.updatePopularityScore = function() {
  // Calculate popularity score based on various factors
  const viewWeight = 1;
  const ratingWeight = 2;
  const helpfulWeight = 3;
  const recentWeight = 1;

  const daysSincePublished = this.publishedAt ?
    Math.max(1, (Date.now() - this.publishedAt.getTime()) / (1000 * 60 * 60 * 24)) : 1;

  const recencyFactor = Math.max(0.1, 1 / Math.log(daysSincePublished + 1));

  this.analytics.popularityScore = (
    (this.analytics.views * viewWeight) +
    (this.analytics.averageRating * this.analytics.totalRatings * ratingWeight) +
    (this.analytics.helpfulVotes * helpfulWeight) +
    (recencyFactor * recentWeight * 100)
  );
};

knowledgeBaseSchema.methods.publish = function(publishedBy) {
  this.status = 'published';
  this.publishedAt = new Date();
  this.lastModifiedBy = publishedBy;
  return this.save();
};

// Legacy methods for backward compatibility
knowledgeBaseSchema.methods.incrementViews = async function() {
  return this.incrementView(false);
};

knowledgeBaseSchema.methods.markAsHelpful = async function() {
  return this.addFeedback(null, null, true);
};

// Static methods
knowledgeBaseSchema.statics.getPublishedArticles = function(category = null, limit = 20) {
  const query = { status: 'published', visibility: { $in: ['public', 'customer_only'] } };
  if (category) {
    query.category = category;
  }
  return this.find(query)
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ featured: -1, 'analytics.popularityScore': -1 })
    .limit(limit);
};

knowledgeBaseSchema.statics.getFeaturedArticles = function(limit = 5) {
  return this.find({
    status: 'published',
    featured: true,
    visibility: { $in: ['public', 'customer_only'] }
  })
  .populate('category', 'name slug')
  .sort({ priority: -1, publishedAt: -1 })
  .limit(limit);
};

knowledgeBaseSchema.statics.getPopularArticles = function(limit = 10) {
  return this.find({
    status: 'published',
    visibility: { $in: ['public', 'customer_only'] }
  })
  .populate('category', 'name slug')
  .sort({ 'analytics.popularityScore': -1 })
  .limit(limit);
};

knowledgeBaseSchema.statics.searchArticles = function(query, options = {}) {
  const searchQuery = {
    $text: { $search: query },
    status: 'published',
    visibility: { $in: ['public', 'customer_only'] }
  };

  if (options.category) {
    searchQuery.category = options.category;
  }

  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .populate('category', 'name slug')
    .sort({ score: { $meta: 'textScore' } })
    .limit(options.limit || 20);
};

// Pre-save middleware
knowledgeBaseSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('title')) {
    // Generate slug from title
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  if (this.isNew || this.isModified('content')) {
    // Update estimated read time
    this.metadata.estimatedReadTime = this.estimatedReadTime;
  }

  if (this.isModified('content') || this.isModified('title')) {
    this.metadata.lastUpdated = new Date();
    this.lastUpdated = new Date(); // Legacy field
  }

  next();
});

knowledgeBaseCategorySchema.pre('save', function(next) {
  if (this.isNew || this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  next();
});

export const KnowledgeBase = mongoose.model('KnowledgeBase', knowledgeBaseSchema);
export const KnowledgeBaseCategory = mongoose.model('KnowledgeBaseCategory', knowledgeBaseCategorySchema);