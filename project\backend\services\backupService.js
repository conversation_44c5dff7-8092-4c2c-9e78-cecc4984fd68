import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import zlib from 'zlib';
import { promisify } from 'util';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { exec } from 'child_process';
import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';

const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);
const execAsync = promisify(exec);

export class BackupService {
  constructor(config = {}) {
    this.config = {
      backupDir: config.backupDir || path.join(process.cwd(), 'backups'),
      maxBackups: config.maxBackups || 30,
      compressionLevel: config.compressionLevel || 6,
      encryptionEnabled: config.encryptionEnabled || true,
      encryptionKey: config.encryptionKey || process.env.BACKUP_ENCRYPTION_KEY,
      retentionPeriod: config.retentionPeriod || 90, // days
      ...config
    };

    this.backupDir = this.config.backupDir;
    this.auditService = new AuditLogService();
    this.ensureBackupDirectory();
    this.versionMap = new Map(); // Track backup versions
    this.lastBackupData = null; // For incremental backups
    this.lastFullBackup = null; // For differential backups
    this.encryptionKey = this.config.encryptionKey || crypto.randomBytes(32);
    this.ivLength = 16; // For AES-256-GCM
    this.compressionLevel = this.config.compressionLevel;
  }

  encryptData(data) {
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipheriv('aes-256-gcm', this.encryptionKey, iv);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag();
    return {
      iv: iv.toString('hex'),
      encryptedData: encrypted,
      authTag: authTag.toString('hex')
    };
  }

  decryptData(encryptedData) {
    try {
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const authTag = Buffer.from(encryptedData.authTag, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-gcm', this.encryptionKey, iv);
      decipher.setAuthTag(authTag);
      let decrypted = decipher.update(encryptedData.encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt backup data');
    }
  }

  validateConfig(config) {
    if (!config || typeof config !== 'object') {
      throw new Error('Invalid configuration provided');
    }
  }

  async ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  validateBackupData(data, type) {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid backup data provided');
    }
    if (!type || typeof type !== 'string') {
      throw new Error('Invalid backup type provided');
    }
    // Sanitize sensitive data
    const sanitizedData = JSON.parse(JSON.stringify(data, (key, value) => {
      if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret')) {
        return '[REDACTED]';
      }
      return value;
    }));
    return sanitizedData;
  }

  async estimateBackupSize(data) {
    const jsonString = JSON.stringify(data);
    const estimatedSize = Buffer.byteLength(jsonString, 'utf8');
    const compressionRatio = 0.6; // Estimated compression ratio
    return Math.ceil(estimatedSize * compressionRatio);
  }

  async processDataChunks(data, chunkSize = 1024 * 1024) { // 1MB chunks by default
    const jsonString = JSON.stringify(data);
    const totalSize = Buffer.byteLength(jsonString, 'utf8');
    const chunks = [];
    let offset = 0;

    while (offset < totalSize) {
      const chunk = jsonString.slice(offset, offset + chunkSize);
      chunks.push(JSON.parse(chunk));
      offset += chunkSize;
    }

    return chunks;
  }

  async createBackup(data, type, userId, backupType = 'full', options = {}) {
    const validBackupTypes = ['full', 'incremental', 'differential'];
    if (!validBackupTypes.includes(backupType)) {
      throw new Error('Invalid backup type. Must be one of: full, incremental, differential');
    }
    // Handle database-specific backup
    if (type === 'database') {
      const { dbType, connectionConfig } = options;
      if (!dbType || !connectionConfig) {
        throw new Error('Database type and connection config are required for database backup');
      }

      // Check database consistency before backup
      const consistencyCheck = await this.databaseSchemaService.checkConsistency(connectionConfig, dbType);
      if (!consistencyCheck.isConsistent) {
        throw new Error(`Database consistency check failed: ${JSON.stringify(consistencyCheck.issues)}`);
      }

      // Track schema version
      await this.databaseSchemaService.trackSchemaVersion(connectionConfig, dbType);

      // Perform database-specific backup
      let backupResult;
      switch (dbType.toLowerCase()) {
        case 'mysql':
          backupResult = await this.databaseBackupService.backupMySQL(connectionConfig, options);
          break;
        case 'mongodb':
          backupResult = await this.databaseBackupService.backupMongoDB(connectionConfig, options);
          break;
        case 'postgresql':
          backupResult = await this.databaseBackupService.backupPostgreSQL(connectionConfig, options);
          break;
        default:
          throw new Error('Unsupported database type');
      }

      data = { ...backupResult, dbType, connectionConfig };
    }
    try {
      const sanitizedData = this.validateBackupData(data, type);
      const estimatedSize = await this.estimateBackupSize(sanitizedData);
      const timestamp = new Date().toISOString();
      const backupId = crypto.randomUUID();

      // Start tracking backup progress with size estimation
      this.monitorService.trackBackup(backupId, type, { estimatedSize });

      let backupData = {
        id: backupId,
        type,
        createdAt: timestamp,
        version: 1,
        isIncremental: false,
        parentBackupId: null,
        size: estimatedSize
      };

      if (backupType === 'incremental' && this.lastBackupData) {
        const changes = this.calculateChanges(this.lastBackupData.data, sanitizedData);
        if (Object.keys(changes).length > 0) {
          backupData.backupType = 'incremental';
          backupData.parentBackupId = this.lastBackupData.id;
          backupData.version = this.lastBackupData.version + 1;
          sanitizedData = changes;
        }
      } else if (backupType === 'differential' && this.lastFullBackup) {
        const changes = this.calculateChanges(this.lastFullBackup.data, sanitizedData);
        if (Object.keys(changes).length > 0) {
          backupData.backupType = 'differential';
          backupData.parentBackupId = this.lastFullBackup.id;
          backupData.version = this.lastFullBackup.version + 1;
          sanitizedData = changes;
        }
      } else if (backupType === 'full') {
        this.lastFullBackup = { ...backupData, data: sanitizedData };
      }
      }

      // Process data in chunks if it's large
      if (estimatedSize > 5 * 1024 * 1024) { // 5MB threshold
        const chunks = await this.processDataChunks(sanitizedData);
        let processedSize = 0;

        for (let i = 0; i < chunks.length; i++) {
          const chunk = chunks[i];
          const encryptedChunk = this.encryptData(chunk);
          const chunkId = `${backupId}_chunk_${i}`;
          const chunkPath = path.join(this.backupDir, `${chunkId}.json`);

          const chunkContent = JSON.stringify({
            id: chunkId,
            parentId: backupId,
            index: i,
            total: chunks.length,
            data: encryptedChunk
          });

          const compressedChunk = await gzip(chunkContent, { level: this.compressionLevel });
          await writeFile(chunkPath, compressedChunk);

          processedSize += Buffer.byteLength(chunkContent);
          const progress = Math.round((processedSize / estimatedSize) * 100);

          this.monitorService.emit('backupProgress', {
            backupId,
            progress,
            message: `Processing chunk ${i + 1} of ${chunks.length}...`
          });
        }

        backupData.chunked = true;
        backupData.totalChunks = chunks.length;
      } else {
        // Original single-file backup logic
        const encryptedData = this.encryptData(sanitizedData);
        backupData.data = encryptedData;
        backupData.chunked = false;
      }

      backupData.checksum = this.generateChecksum(sanitizedData);
      this.lastBackupData = { ...backupData, data: sanitizedData };
      this.versionMap.set(backupId, backupData.version);

      const backupContent = JSON.stringify(backupData);
      const compressedContent = await gzip(backupContent, { level: this.compressionLevel });
      const backupPath = path.join(this.backupDir, `${backupId}.json`);

      await writeFile(backupPath, compressedContent);

      if (this.config.storageProvider && this.config.storageProvider !== 'local') {
        await this.uploadToCloud(backupId, compressedContent);
      }

      await this.cacheService.delete('backups:list');

      this.monitorService.emit('backupComplete', { backupId });

      return {
        id: backupId,
        type,
        status: 'completed',
        createdAt: timestamp,
        provider: this.config.storageProvider,
        size: estimatedSize,
        chunked: backupData.chunked
      };
    } catch (error) {
      console.error('Backup creation failed:', error);
      this.monitorService.emit('backupError', {
        backupId,
        error: error.message || 'Failed to create backup'
      });
      throw new Error('Failed to create backup');
    }
  }

  async restoreBackup(backupId, userId, options = {}) {
    try {
      const backupPath = path.join(this.backupDir, `${backupId}.json`);
      const backupContent = await readFile(backupPath, 'utf8');
      const backup = JSON.parse(backupContent);

      // Check user permissions
      const hasPermission = await this.permissionService.hasPermission(userId, backupId, 'write');
      if (!hasPermission) {
        throw new Error('User does not have permission to restore this backup');
      }

      // Decrypt the data
      const decryptedData = this.decryptData(backup.data);

      // Verify backup integrity
      const storedChecksum = backup.checksum;
      const calculatedChecksum = this.generateChecksum(decryptedData);

      if (storedChecksum !== calculatedChecksum) {
        throw new Error('Backup integrity check failed');
      }

      // Handle partial restore if specified
      let dataToRestore = decryptedData;
      if (options.partial && options.paths) {
        dataToRestore = this.extractPartialData(decryptedData, options.paths);
      }

      // Perform actual restore
      const restorePath = options.restorePath || path.join(this.backupDir, 'restored');
      if (!fs.existsSync(restorePath)) {
        fs.mkdirSync(restorePath, { recursive: true });
      }

      // Write restored data
      const restoreFilePath = path.join(restorePath, `${backupId}_restored.json`);
      await writeFile(restoreFilePath, JSON.stringify(dataToRestore, null, 2));

      // Log restore operation
      await this.auditLogService.logAction({
        userId,
        action: 'restore',
        resourceId: backupId,
        details: {
          partial: !!options.partial,
          paths: options.paths,
          restorePath
        }
      });

      return {
        status: 'restored',
        path: restoreFilePath,
        timestamp: new Date().toISOString(),
        partial: !!options.partial
      };
    } catch (error) {
      console.error('Backup restore failed:', error);
      await this.errorTrackingService.trackError(error, { backupId, userId });
      throw new Error('Failed to restore backup');
    }
  }

  extractPartialData(data, paths) {
    const result = {};
    for (const path of paths) {
      const keys = path.split('.');
      let current = data;
      let target = result;

      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        if (i === keys.length - 1) {
          target[key] = current[key];
        } else {
          current = current[key];
          target[key] = target[key] || {};
          target = target[key];
        }
      }
    }
    return result;
  }

  async scheduleVerification(schedule = '0 0 * * *') {
    const verificationJob = new CronJob(schedule, async () => {
      try {
        const backups = await this.listBackups();
        for (const backup of backups) {
          try {
            await this.verifyBackup(backup.id);
          } catch (error) {
            await this.errorTrackingService.trackError(error, {
              backupId: backup.id,
              operation: 'verification'
            });
          }
        }
      } catch (error) {
        console.error('Scheduled verification failed:', error);
      }
    });

    verificationJob.start();
    return verificationJob;
  }

  async verifyBackup(backupId) {
    try {
      const backupPath = path.join(this.backupDir, `${backupId}.json`);
      const backupContent = await readFile(backupPath, 'utf8');
      const backup = JSON.parse(backupContent);

      // Decrypt and verify
      const decryptedData = this.decryptData(backup.data);
      const calculatedChecksum = this.generateChecksum(decryptedData);

      const isValid = backup.checksum === calculatedChecksum;

      await this.auditLogService.logAction({
        action: 'verify',
        resourceId: backupId,
        details: { isValid }
      });

      return {
        status: isValid ? 'valid' : 'invalid',
        timestamp: new Date().toISOString(),
        backupId
      };
    } catch (error) {
      console.error('Backup verification failed:', error);
      throw new Error('Failed to verify backup');
    }
  }

  async uploadToCloud(backupId, content, userId) {
    const key = `backups/${backupId}.json`;
    const context = { backupId };

    return await this.errorTrackingService.retryOperation(async () => {
      const result = await this.cloudProviderService.uploadFile(
        process.env.CLOUD_STORAGE_BUCKET,
        key,
        content,
        { contentType: 'application/json' }
      );

      await this.auditLogService.logAction({
        userId,
        action: 'upload',
        resourceId: backupId,
        details: {
          provider: result.provider,
          checksum: result.checksum
        }
      });

      return result;
    }, context);
  }

  async pointInTimeRecover(timestamp, options = {}) {
    try {
      const { type, transactionLog = true } = options;
      const backups = await this.listBackups();

      // Filter backups by type if specified
      let filteredBackups = type
        ? backups.filter(backup => backup.type === type)
        : backups;

      // Find the most recent backup before the target timestamp
      const targetBackup = filteredBackups
        .filter(backup => new Date(backup.createdAt) <= new Date(timestamp))
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

      if (!targetBackup) {
        throw new Error(`No ${type || ''} backup found for the specified point in time`);
      }

      // Restore to the backup point
      const restoredBackup = await this.restoreBackup(targetBackup.id);

      // If transaction log recovery is enabled and we have a transaction log service
      if (transactionLog && this.transactionLogService) {
        const backupTime = new Date(targetBackup.createdAt);
        const targetTime = new Date(timestamp);

        // Only replay transactions if target time is after backup time
        if (targetTime > backupTime) {
          const handlers = {
            'database': this.handleDatabaseTransaction.bind(this),
            'files': this.handleFileTransaction.bind(this),
            'configuration': this.handleConfigTransaction.bind(this)
          };

          // Replay transactions between backup time and target time
          const replayResults = await this.transactionLogService.replayTransactions(
            backupTime,
            targetTime,
            handlers
          );

          return {
            ...restoredBackup,
            transactionReplay: {
              startTime: backupTime,
              endTime: targetTime,
              results: replayResults
            }
          };
        }
      }

      return restoredBackup;
    } catch (error) {
      console.error('Point-in-time recovery failed:', error);
      throw new Error('Failed to perform point-in-time recovery');
    }
  }

  async handleDatabaseTransaction(transaction) {
    // Implement database-specific transaction replay
    const { operation, data } = transaction;
    switch (operation) {
      case 'insert':
      case 'update':
      case 'delete':
        return await this.databaseBackupService.replayTransaction(transaction);
      default:
        throw new Error(`Unsupported database operation: ${operation}`);
    }
  }

  async handleFileTransaction(transaction) {
    // Implement file-specific transaction replay
    const { operation, path: filePath, content } = transaction;
    switch (operation) {
      case 'create':
      case 'update':
        await writeFile(filePath, content);
        return { status: 'success', path: filePath };
      case 'delete':
        await fs.promises.unlink(filePath);
        return { status: 'success', path: filePath };
      default:
        throw new Error(`Unsupported file operation: ${operation}`);
    }
  }

  async handleConfigTransaction(transaction) {
    // Implement configuration-specific transaction replay
    const { operation, key, value } = transaction;
    switch (operation) {
      case 'set':
        await this.databaseSchemaService.updateConfig(key, value);
        return { status: 'success', key };
      case 'delete':
        await this.databaseSchemaService.deleteConfig(key);
        return { status: 'success', key };
      default:
        throw new Error(`Unsupported configuration operation: ${operation}`);
    }
  }
  }

  async replicateToSecondaryRegion(backupId) {
    if (!this.s3Replica) {
      throw new Error('Secondary region not configured');
    }

    try {
      const backupPath = path.join(this.backupDir, `${backupId}.json`);
      const backupContent = await readFile(backupPath);

      await this.s3Replica.putObject({
        Bucket: process.env.AWS_SECONDARY_BUCKET_NAME,
        Key: `backups/${backupId}.json`,
        Body: backupContent,
        ContentType: 'application/json'
      }).promise();

      return {
        status: 'replicated',
        region: process.env.AWS_SECONDARY_REGION,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Backup replication failed:', error);
      throw new Error('Failed to replicate backup to secondary region');
    }
  }

  async deleteBackup(backupId, userId) {
    try {
      const backupPath = path.join(this.backupDir, `${backupId}.json`);
      await fs.promises.unlink(backupPath);
    } catch (error) {
      console.error('Backup deletion failed:', error);
      throw new Error('Failed to delete backup');
    }
  }

  async listBackups() {
    try {
      const cacheKey = 'backups:list';
      const cachedBackups = await this.cacheService.get(cacheKey);

      if (cachedBackups) {
        return cachedBackups;
      }

      const files = await fs.promises.readdir(this.backupDir);
      const backups = await Promise.all(
        files.map(async (file) => {
          const content = await readFile(path.join(this.backupDir, file), 'utf8');
          const backup = JSON.parse(content);
          const stats = await fs.promises.stat(path.join(this.backupDir, file));

          return {
            id: backup.id,
            type: backup.type,
            createdAt: backup.createdAt,
            size: stats.size,
            status: 'completed'
          };
        })
      );

      await this.cacheService.set(cacheKey, backups, 300); // Cache for 5 minutes
      return backups;
    } catch (error) {
      console.error('Failed to list backups:', error);
      throw new Error('Failed to list backups');
    }
  }

  async applyRetentionPolicy() {
    try {
      const backups = await this.listBackups();
      const { retention, maxBackups } = this.config;

      if (!retention || !maxBackups) return;

      const sortedBackups = backups.sort((a, b) =>
        new Date(b.createdAt) - new Date(a.createdAt)
      );

      const retentionDate = new Date();
      retentionDate.setDate(retentionDate.getDate() - retention);

      for (const backup of sortedBackups) {
        if (sortedBackups.indexOf(backup) >= maxBackups ||
            new Date(backup.createdAt) < retentionDate) {
          await this.deleteBackup(backup.id);
        }
      }
    } catch (error) {
      console.error('Failed to apply retention policy:', error);
      throw new Error('Failed to apply retention policy');
    }
  }

  generateChecksum(data) {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }
  // ===== GLOBAL BACKUP METHODS =====

  async initiateGlobalBackup(backupType = 'full', retention = 30) {
    try {
      const backupId = this.generateBackupId();
      const timestamp = new Date().toISOString();

      const backup = {
        id: backupId,
        type: backupType,
        status: 'initiated',
        startTime: new Date(),
        retention,
        components: [],
        metadata: {
          version: process.env.APP_VERSION || '1.0.0',
          environment: process.env.NODE_ENV || 'production',
          initiatedBy: 'system'
        }
      };

      // Start backup process
      this.performGlobalBackup(backup);

      await this.auditService.log(
        null,
        'system_backup_initiated',
        { backupId, backupType, retention },
        'info'
      );

      logger.info(`Global backup initiated: ${backupId}`);
      return backup;
    } catch (error) {
      logger.error('Error initiating global backup:', error);
      throw error;
    }
  }

  async performGlobalBackup(backup) {
    try {
      backup.status = 'running';
      backup.progress = 0;

      // Backup components
      const components = [
        { name: 'database', handler: this.backupDatabase.bind(this) },
        { name: 'files', handler: this.backupFiles.bind(this) },
        { name: 'configurations', handler: this.backupConfigurations.bind(this) },
        { name: 'logs', handler: this.backupLogs.bind(this) }
      ];

      const totalComponents = components.length;
      let completedComponents = 0;

      for (const component of components) {
        try {
          logger.info(`Starting backup of ${component.name}`);
          const result = await component.handler(backup.id);

          backup.components.push({
            name: component.name,
            status: 'completed',
            size: result.size,
            path: result.path,
            checksum: result.checksum,
            completedAt: new Date()
          });

          completedComponents++;
          backup.progress = Math.round((completedComponents / totalComponents) * 100);

          logger.info(`Completed backup of ${component.name}`);
        } catch (error) {
          logger.error(`Error backing up ${component.name}:`, error);
          backup.components.push({
            name: component.name,
            status: 'failed',
            error: error.message,
            failedAt: new Date()
          });
        }
      }

      // Finalize backup
      backup.status = 'completed';
      backup.endTime = new Date();
      backup.duration = Math.floor((backup.endTime - backup.startTime) / 1000);
      backup.totalSize = backup.components.reduce((sum, comp) => sum + (comp.size || 0), 0);

      // Create backup manifest
      await this.createBackupManifest(backup);

      // Cleanup old backups
      await this.cleanupOldBackups();

      await this.auditService.log(
        null,
        'system_backup_completed',
        {
          backupId: backup.id,
          duration: backup.duration,
          totalSize: backup.totalSize,
          componentsCompleted: backup.components.filter(c => c.status === 'completed').length,
          componentsFailed: backup.components.filter(c => c.status === 'failed').length
        },
        'info'
      );

      logger.info(`Global backup completed: ${backup.id}`);
      return backup;
    } catch (error) {
      backup.status = 'failed';
      backup.error = error.message;
      backup.endTime = new Date();

      logger.error('Error performing global backup:', error);
      throw error;
    }
  }

  async backupDatabase(backupId) {
    try {
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 27017,
        database: process.env.DB_NAME || 'nextgenpanel',
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD
      };

      const backupPath = path.join(this.backupDir, `${backupId}_database.gz`);

      // MongoDB backup command
      let command = `mongodump --host ${dbConfig.host}:${dbConfig.port} --db ${dbConfig.database}`;

      if (dbConfig.username && dbConfig.password) {
        command += ` --username ${dbConfig.username} --password ${dbConfig.password}`;
      }

      command += ` --archive=${backupPath} --gzip`;

      const { stdout, stderr } = await execAsync(command);

      if (stderr && !stderr.includes('done dumping')) {
        throw new Error(`Database backup failed: ${stderr}`);
      }

      const stats = fs.statSync(backupPath);
      const checksum = await this.calculateChecksum(backupPath);

      return {
        path: backupPath,
        size: stats.size,
        checksum
      };
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }

  async backupFiles(backupId) {
    try {
      const sourcePaths = [
        path.join(process.cwd(), 'uploads'),
        path.join(process.cwd(), 'public'),
        path.join(process.cwd(), 'storage')
      ];

      const backupPath = path.join(this.backupDir, `${backupId}_files.tar.gz`);

      // Create tar archive of files
      const existingPaths = sourcePaths.filter(p => fs.existsSync(p));

      if (existingPaths.length === 0) {
        logger.warn('No file directories found to backup');
        return { path: null, size: 0, checksum: null };
      }

      const command = `tar -czf ${backupPath} ${existingPaths.join(' ')}`;
      const { stdout, stderr } = await execAsync(command);

      if (stderr) {
        logger.warn('File backup warnings:', stderr);
      }

      const stats = fs.statSync(backupPath);
      const checksum = await this.calculateChecksum(backupPath);

      return {
        path: backupPath,
        size: stats.size,
        checksum
      };
    } catch (error) {
      logger.error('File backup failed:', error);
      throw error;
    }
  }

  async backupConfigurations(backupId) {
    try {
      const configPaths = [
        path.join(process.cwd(), '.env'),
        path.join(process.cwd(), 'config'),
        path.join(process.cwd(), 'package.json'),
        path.join(process.cwd(), 'docker-compose.yml')
      ];

      const backupPath = path.join(this.backupDir, `${backupId}_config.tar.gz`);

      const existingPaths = configPaths.filter(p => fs.existsSync(p));

      if (existingPaths.length === 0) {
        logger.warn('No configuration files found to backup');
        return { path: null, size: 0, checksum: null };
      }

      const command = `tar -czf ${backupPath} ${existingPaths.join(' ')}`;
      await execAsync(command);

      const stats = fs.statSync(backupPath);
      const checksum = await this.calculateChecksum(backupPath);

      return {
        path: backupPath,
        size: stats.size,
        checksum
      };
    } catch (error) {
      logger.error('Configuration backup failed:', error);
      throw error;
    }
  }

  async backupLogs(backupId) {
    try {
      const logPaths = [
        path.join(process.cwd(), 'logs'),
        path.join(process.cwd(), 'var/log')
      ];

      const backupPath = path.join(this.backupDir, `${backupId}_logs.tar.gz`);

      const existingPaths = logPaths.filter(p => fs.existsSync(p));

      if (existingPaths.length === 0) {
        logger.warn('No log directories found to backup');
        return { path: null, size: 0, checksum: null };
      }

      const command = `tar -czf ${backupPath} ${existingPaths.join(' ')}`;
      await execAsync(command);

      const stats = fs.statSync(backupPath);
      const checksum = await this.calculateChecksum(backupPath);

      return {
        path: backupPath,
        size: stats.size,
        checksum
      };
    } catch (error) {
      logger.error('Log backup failed:', error);
      throw error;
    }
  }

  async createBackupManifest(backup) {
    try {
      const manifestPath = path.join(this.backupDir, `${backup.id}_manifest.json`);
      const manifest = {
        ...backup,
        createdAt: new Date().toISOString(),
        version: '1.0',
        format: 'nextgenpanel-backup'
      };

      fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
      logger.info(`Backup manifest created: ${manifestPath}`);
    } catch (error) {
      logger.error('Error creating backup manifest:', error);
      throw error;
    }
  }

  async getBackupStatus(backupId) {
    try {
      const manifestPath = path.join(this.backupDir, `${backupId}_manifest.json`);

      if (!fs.existsSync(manifestPath)) {
        return { status: 'not_found' };
      }

      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      return manifest;
    } catch (error) {
      logger.error('Error getting backup status:', error);
      throw error;
    }
  }

  async configureSettings(settings) {
    try {
      this.config = { ...this.config, ...settings };

      await this.auditService.log(
        null,
        'system_configuration_changed',
        { component: 'backup_service', settings },
        'info'
      );

      logger.info('Backup service settings updated');
      return this.config;
    } catch (error) {
      logger.error('Error configuring backup settings:', error);
      throw error;
    }
  }

  async restoreFromBackup(backupId, targetLocation, options = {}) {
    try {
      const manifest = await this.getBackupStatus(backupId);

      if (manifest.status === 'not_found') {
        throw new Error(`Backup ${backupId} not found`);
      }

      if (manifest.status !== 'completed') {
        throw new Error(`Backup ${backupId} is not in completed state`);
      }

      const restoration = {
        id: this.generateBackupId(),
        backupId,
        status: 'initiated',
        startTime: new Date(),
        targetLocation,
        options
      };

      // Start restoration process
      this.performRestore(restoration, manifest);

      await this.auditService.log(
        null,
        'system_backup_restored',
        { backupId, restorationId: restoration.id, targetLocation },
        'warning'
      );

      return restoration;
    } catch (error) {
      logger.error('Error initiating backup restoration:', error);
      throw error;
    }
  }

  async performRestore(restoration, manifest) {
    try {
      restoration.status = 'running';

      // Restore each component
      for (const component of manifest.components) {
        if (component.status === 'completed' && component.path) {
          logger.info(`Restoring ${component.name}`);
          await this.restoreComponent(component, restoration.targetLocation);
        }
      }

      restoration.status = 'completed';
      restoration.endTime = new Date();
      restoration.duration = Math.floor((restoration.endTime - restoration.startTime) / 1000);

      logger.info(`Backup restoration completed: ${restoration.id}`);
      return restoration;
    } catch (error) {
      restoration.status = 'failed';
      restoration.error = error.message;
      restoration.endTime = new Date();

      logger.error('Error performing backup restoration:', error);
      throw error;
    }
  }

  async restoreComponent(component, targetLocation) {
    // Implementation would depend on component type
    // This is a simplified version
    logger.info(`Restoring component ${component.name} from ${component.path}`);
  }

  async cleanupOldBackups() {
    try {
      const files = fs.readdirSync(this.backupDir);
      const manifestFiles = files.filter(f => f.endsWith('_manifest.json'));

      const backups = [];
      for (const file of manifestFiles) {
        try {
          const manifest = JSON.parse(
            fs.readFileSync(path.join(this.backupDir, file), 'utf8')
          );
          backups.push(manifest);
        } catch (error) {
          logger.warn(`Error reading manifest ${file}:`, error);
        }
      }

      // Sort by creation date
      backups.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

      // Keep only the most recent backups
      const backupsToDelete = backups.slice(this.config.maxBackups);

      for (const backup of backupsToDelete) {
        await this.deleteBackup(backup.id);
      }

      logger.info(`Cleaned up ${backupsToDelete.length} old backups`);
    } catch (error) {
      logger.error('Error cleaning up old backups:', error);
    }
  }

  async deleteBackup(backupId) {
    try {
      const files = fs.readdirSync(this.backupDir);
      const backupFiles = files.filter(f => f.startsWith(backupId));

      for (const file of backupFiles) {
        const filePath = path.join(this.backupDir, file);
        fs.unlinkSync(filePath);
      }

      logger.info(`Deleted backup: ${backupId}`);
    } catch (error) {
      logger.error(`Error deleting backup ${backupId}:`, error);
    }
  }

  generateBackupId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = crypto.randomBytes(4).toString('hex');
    return `backup-${timestamp}-${random}`;
  }

  async calculateChecksum(filePath) {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const stream = fs.createReadStream(filePath);

      stream.on('data', data => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }
}