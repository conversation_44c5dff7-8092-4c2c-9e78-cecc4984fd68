import mongoose from 'mongoose';

const auditLogSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      // User actions
      'user_login', 'user_logout', 'user_created', 'user_updated', 'user_deleted',
      'user_suspended', 'user_activated', 'user_role_changed', 'user_password_changed',
      'user_password_reset', 'user_mfa_enabled', 'user_mfa_disabled', 'user_impersonated',

      // Admin actions
      'admin_login', 'admin_logout', 'admin_action_performed', 'admin_settings_changed',
      'admin_user_managed', 'admin_role_assigned', 'admin_permission_granted',

      // Security actions
      'security_policy_updated', 'security_scan_initiated', 'security_scan_completed',
      'firewall_rule_added', 'firewall_rule_removed', 'ip_whitelist_updated',
      'security_alert_triggered', 'security_incident_created', 'security_incident_resolved',

      // System actions
      'system_backup_created', 'system_backup_restored', 'system_update_applied',
      'system_configuration_changed', 'system_service_started', 'system_service_stopped',
      'system_maintenance_started', 'system_maintenance_completed',

      // Data actions
      'data_exported', 'data_imported', 'data_deleted', 'data_modified',
      'database_query_executed', 'file_uploaded', 'file_downloaded', 'file_deleted',

      // Billing actions
      'billing_plan_changed', 'payment_processed', 'invoice_generated', 'refund_processed',
      'subscription_created', 'subscription_cancelled', 'subscription_renewed',

      // API actions
      'api_key_created', 'api_key_revoked', 'api_request_made', 'webhook_triggered',
      'rate_limit_exceeded', 'api_authentication_failed',

      // Infrastructure actions
      'server_created', 'server_deleted', 'server_updated', 'service_deployed',
      'container_started', 'container_stopped', 'load_balancer_configured',

      // Compliance actions
      'compliance_check_performed', 'audit_report_generated', 'policy_violation_detected',
      'data_retention_policy_applied', 'gdpr_request_processed',

      // Legacy actions for backward compatibility
      'CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT'
    ]
  },
  resource: {
    type: String, // What resource was affected (user ID, server ID, etc.)
  },
  resourceType: {
    type: String,
    enum: [
      'user', 'role', 'permission', 'server', 'database', 'file', 'backup',
      'security_policy', 'firewall_rule', 'api_key', 'webhook', 'invoice',
      'subscription', 'ticket', 'announcement', 'report', 'configuration'
    ]
  },
  module: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  details: {
    type: mongoose.Schema.Types.Mixed, // Flexible object for action-specific details
    default: {}
  },
  severity: {
    type: String,
    enum: ['info', 'warning', 'error', 'critical'],
    default: 'info'
  },
  category: {
    type: String,
    enum: [
      'authentication', 'authorization', 'data_access', 'data_modification',
      'system_administration', 'security', 'compliance', 'billing',
      'infrastructure', 'api', 'user_management', 'configuration'
    ],
    default: 'system_administration'
  },
  outcome: {
    type: String,
    enum: ['success', 'failure', 'partial', 'blocked', 'pending'],
    default: 'success'
  },
  ipAddress: {
    type: String,
    validate: {
      validator: function(v) {
        if (!v) return true;
        // Basic IP validation (IPv4 and IPv6)
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(v) || ipv6Regex.test(v);
      },
      message: 'Invalid IP address format'
    }
  },
  userAgent: String,
  sessionId: String,
  requestId: String,
  correlationId: String, // For tracking related actions
  geolocation: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    timezone: String
  },
  device: {
    type: String,
    browser: String,
    os: String,
    platform: String
  },
  api: {
    endpoint: String,
    method: String,
    statusCode: Number,
    responseTime: Number, // milliseconds
    requestSize: Number,  // bytes
    responseSize: Number  // bytes
  },
  changes: {
    before: mongoose.Schema.Types.Mixed,
    after: mongoose.Schema.Types.Mixed,
    fields: [String] // List of changed fields
  },
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  risk: {
    score: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    factors: [String], // Risk factors that contributed to the score
    automated: {
      type: Boolean,
      default: true
    }
  },
  alerts: {
    triggered: {
      type: Boolean,
      default: false
    },
    rules: [{
      ruleId: String,
      ruleName: String,
      severity: String
    }]
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  }
}, {
  timestamps: true
});

// Indexes for performance
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ category: 1, timestamp: -1 });
auditLogSchema.index({ severity: 1, timestamp: -1 });
auditLogSchema.index({ outcome: 1, timestamp: -1 });
auditLogSchema.index({ ipAddress: 1, timestamp: -1 });
auditLogSchema.index({ sessionId: 1 });
auditLogSchema.index({ correlationId: 1 });
auditLogSchema.index({ 'risk.score': -1, timestamp: -1 });
auditLogSchema.index({ 'alerts.triggered': 1, timestamp: -1 });

// Compound indexes
auditLogSchema.index({ userId: 1, action: 1, timestamp: -1 });
auditLogSchema.index({ category: 1, severity: 1, timestamp: -1 });
auditLogSchema.index({ resourceType: 1, resource: 1, timestamp: -1 });

// Legacy index for backward compatibility
auditLogSchema.index({ userId: 1, action: 1, module: 1, createdAt: 1 });

// Methods
auditLogSchema.methods.calculateRiskScore = function() {
  let score = 0;

  // Base score by action type
  const actionRiskScores = {
    'user_deleted': 80,
    'admin_action_performed': 60,
    'security_policy_updated': 70,
    'firewall_rule_removed': 75,
    'data_deleted': 85,
    'system_configuration_changed': 65,
    'api_authentication_failed': 40,
    'compliance_check_performed': 30,
    'DELETE': 70, // Legacy action
    'UPDATE': 30, // Legacy action
    'CREATE': 20  // Legacy action
  };

  score += actionRiskScores[this.action] || 10;

  // Severity multiplier
  const severityMultipliers = {
    'info': 1,
    'warning': 1.5,
    'error': 2.5,
    'critical': 4
  };

  score *= severityMultipliers[this.severity] || 1;

  // Outcome factor
  if (this.outcome === 'failure') score *= 1.3;
  if (this.outcome === 'blocked') score *= 0.5;

  // Time-based factor (recent actions are riskier)
  const hoursSinceAction = (Date.now() - this.timestamp.getTime()) / (1000 * 60 * 60);
  if (hoursSinceAction < 1) score *= 1.5;
  else if (hoursSinceAction < 24) score *= 1.2;

  this.risk.score = Math.min(Math.round(score), 100);
  return this.risk.score;
};

auditLogSchema.methods.shouldTriggerAlert = function() {
  const alertThresholds = {
    'critical': 50,
    'error': 70,
    'warning': 85,
    'info': 95
  };

  return this.risk.score >= (alertThresholds[this.severity] || 95);
};

// Static methods
auditLogSchema.statics.getByUser = function(userId, limit = 100) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('userId', 'username email');
};

auditLogSchema.statics.getHighRiskActions = function(timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  return this.find({
    timestamp: { $gte: startTime },
    'risk.score': { $gte: 70 }
  }).sort({ 'risk.score': -1, timestamp: -1 });
};

auditLogSchema.statics.getFailedActions = function(timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  return this.find({
    timestamp: { $gte: startTime },
    outcome: 'failure'
  }).sort({ timestamp: -1 });
};

// Pre-save middleware
auditLogSchema.pre('save', function(next) {
  if (this.isNew) {
    // Calculate risk score
    this.calculateRiskScore();

    // Check if alerts should be triggered
    if (this.shouldTriggerAlert()) {
      this.alerts.triggered = true;
    }
  }
  next();
});

export const AuditLog = mongoose.model('AuditLog', auditLogSchema);