import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger.js';
import { RBACService } from '../services/rbacService.js';
import { MFAService } from '../services/mfaService.js';
import { BillingService } from '../services/billingService.js';
import { AnalyticsService } from '../services/analyticsService.js';
import { SecurityService } from '../services/securityService.js';
import { BackupService } from '../services/backupService.js';
import { NotificationService } from '../services/notificationService.js';
import { TenantManagementService } from '../services/tenantManagementService.js';
import { AuditLogService } from '../services/auditLogService.js';
import { ComplianceService } from '../services/ComplianceService.js';
import { User } from '../models/User.js';
import { SupportTicket } from '../models/SupportTicket.js';
import { KnowledgeBase } from '../models/KnowledgeBase.js';

const rbacService = new RBACService();
const mfaService = new MFAService();
const billingService = new BillingService();
const analyticsService = new AnalyticsService();
const securityService = new SecurityService();
const backupService = new BackupService();
const notificationService = new NotificationService();
const tenantService = new TenantManagementService();
const auditService = new AuditLogService();
const complianceService = new ComplianceService();

// ===== USER MANAGEMENT =====

export const getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, status, search } = req.query;
    const query = {};

    if (role) query.role = role;
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(query)
      .select('-password')
      .populate('roles')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await User.countDocuments(query);

    await auditService.log(req.user.id, 'users_viewed', { query });

    res.json({
      users,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    logger.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
};

export const createUser = async (req, res) => {
  try {
    const { username, email, password, role, twoFactorEnabled, limits } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ email }, { username }] });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    const user = new User({
      username,
      email,
      password, // Will be hashed by pre-save middleware
      role,
      twoFactorEnabled,
      limits,
      createdBy: req.user.id,
      status: 'active'
    });

    await user.save();
    await auditService.log(req.user.id, 'user_created', { userId: user.id, role });

    res.status(201).json({
      message: 'User created successfully',
      user: user.toObject({ transform: (doc, ret) => { delete ret.password; return ret; } })
    });
  } catch (error) {
    logger.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
};

export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove sensitive fields that shouldn't be updated directly
    delete updates.password;
    delete updates._id;

    const user = await User.findByIdAndUpdate(id, updates, { new: true }).select('-password');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'user_updated', { userId: id, updates });

    res.json({ message: 'User updated successfully', user });
  } catch (error) {
    logger.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
};

export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByIdAndDelete(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'user_deleted', { userId: id });

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    logger.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
};

export const suspendUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason, duration } = req.body;

    const user = await User.findByIdAndUpdate(
      id,
      {
        status: 'suspended',
        suspensionReason: reason,
        suspendedAt: new Date(),
        suspensionDuration: duration
      },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'user_suspended', { userId: id, reason, duration });

    res.json({ message: 'User suspended successfully', user });
  } catch (error) {
    logger.error('Error suspending user:', error);
    res.status(500).json({ error: 'Failed to suspend user' });
  }
};

export const updateUserRole = async (req, res) => {
  try {
    const { userId, role } = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { role },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'user_role_updated', { userId, newRole: role });

    res.json({ message: 'User role updated successfully', user });
  } catch (error) {
    logger.error('Error updating user role:', error);
    res.status(500).json({ error: 'Failed to update user role' });
  }
};

export const enforceGlobalMFA = async (req, res) => {
  try {
    const { enabled } = req.body;

    await User.updateMany({}, { twoFactorEnabled: enabled });
    await auditService.log(req.user.id, 'global_mfa_enforced', { enabled });

    res.json({ message: `Global MFA ${enabled ? 'enabled' : 'disabled'} successfully` });
  } catch (error) {
    logger.error('Error enforcing global MFA:', error);
    res.status(500).json({ error: 'Failed to enforce global MFA' });
  }
};

export const impersonateUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id).select('-password');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create impersonation token
    const impersonationToken = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        impersonatedBy: req.user.id
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    await auditService.log(req.user.id, 'user_impersonated', { targetUserId: id });

    res.json({
      message: 'Impersonation token generated',
      token: impersonationToken,
      user
    });
  } catch (error) {
    logger.error('Error impersonating user:', error);
    res.status(500).json({ error: 'Failed to impersonate user' });
  }
};

export const getUserActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, limit = 50 } = req.query;

    const query = { userId: id };
    if (startDate && endDate) {
      query.timestamp = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const activities = await auditService.getActivities(query, limit);

    res.json({ activities });
  } catch (error) {
    logger.error('Error fetching user activity:', error);
    res.status(500).json({ error: 'Failed to fetch user activity' });
  }
};

export const getUserLoginHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 20 } = req.query;

    const loginHistory = await auditService.getLoginHistory(id, limit);

    res.json({ loginHistory });
  } catch (error) {
    logger.error('Error fetching user login history:', error);
    res.status(500).json({ error: 'Failed to fetch user login history' });
  }
};

// ===== DEVELOPER & RESELLER MANAGEMENT =====

export const getResellerRequests = async (req, res) => {
  try {
    const { status = 'pending' } = req.query;

    const requests = await User.find({
      'reseller.status': status,
      role: 'reseller'
    }).select('-password');

    res.json({ requests });
  } catch (error) {
    logger.error('Error fetching reseller requests:', error);
    res.status(500).json({ error: 'Failed to fetch reseller requests' });
  }
};

export const approveResellerRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { approved, limits } = req.body;

    const user = await User.findByIdAndUpdate(
      id,
      {
        'reseller.status': approved ? 'approved' : 'rejected',
        'reseller.approvedAt': approved ? new Date() : null,
        'reseller.limits': approved ? limits : null
      },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'reseller_request_processed', { userId: id, approved });

    res.json({ message: 'Reseller request processed successfully', user });
  } catch (error) {
    logger.error('Error processing reseller request:', error);
    res.status(500).json({ error: 'Failed to process reseller request' });
  }
};

export const setResourceLimits = async (req, res) => {
  try {
    const { id } = req.params;
    const { limits } = req.body;

    const user = await User.findByIdAndUpdate(
      id,
      { limits },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'resource_limits_updated', { userId: id, limits });

    res.json({ message: 'Resource limits updated successfully', user });
  } catch (error) {
    logger.error('Error setting resource limits:', error);
    res.status(500).json({ error: 'Failed to set resource limits' });
  }
};

export const getResellerActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;

    const activity = await analyticsService.getResellerActivity(id, startDate, endDate);

    res.json({ activity });
  } catch (error) {
    logger.error('Error fetching reseller activity:', error);
    res.status(500).json({ error: 'Failed to fetch reseller activity' });
  }
};

export const updateWhiteLabeling = async (req, res) => {
  try {
    const { id } = req.params;
    const { branding } = req.body;

    const user = await User.findByIdAndUpdate(
      id,
      { 'reseller.branding': branding },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await auditService.log(req.user.id, 'white_labeling_updated', { userId: id, branding });

    res.json({ message: 'White labeling updated successfully', user });
  } catch (error) {
    logger.error('Error updating white labeling:', error);
    res.status(500).json({ error: 'Failed to update white labeling' });
  }
};

// ===== SERVER & INFRASTRUCTURE MANAGEMENT =====

export const getHostingNodes = async (req, res) => {
  try {
    const nodes = await analyticsService.getHostingNodes();
    res.json({ nodes });
  } catch (error) {
    logger.error('Error fetching hosting nodes:', error);
    res.status(500).json({ error: 'Failed to fetch hosting nodes' });
  }
};

export const addHostingNode = async (req, res) => {
  try {
    const { name, location, specs, provider } = req.body;

    const node = await analyticsService.addHostingNode({
      name,
      location,
      specs,
      provider,
      addedBy: req.user.id
    });

    await auditService.log(req.user.id, 'hosting_node_added', { nodeId: node.id });

    res.status(201).json({ message: 'Hosting node added successfully', node });
  } catch (error) {
    logger.error('Error adding hosting node:', error);
    res.status(500).json({ error: 'Failed to add hosting node' });
  }
};

export const configureWebServer = async (req, res) => {
  try {
    const { serverType, configuration } = req.body;

    await analyticsService.configureWebServer(serverType, configuration);
    await auditService.log(req.user.id, 'webserver_configured', { serverType, configuration });

    res.json({ message: 'Web server configured successfully' });
  } catch (error) {
    logger.error('Error configuring web server:', error);
    res.status(500).json({ error: 'Failed to configure web server' });
  }
};

export const manageLoadBalancer = async (req, res) => {
  try {
    const { action, configuration } = req.body;

    const result = await analyticsService.manageLoadBalancer(action, configuration);
    await auditService.log(req.user.id, 'load_balancer_managed', { action, configuration });

    res.json({ message: 'Load balancer managed successfully', result });
  } catch (error) {
    logger.error('Error managing load balancer:', error);
    res.status(500).json({ error: 'Failed to manage load balancer' });
  }
};

export const getContainerClusters = async (req, res) => {
  try {
    const clusters = await analyticsService.getContainerClusters();
    res.json({ clusters });
  } catch (error) {
    logger.error('Error fetching container clusters:', error);
    res.status(500).json({ error: 'Failed to fetch container clusters' });
  }
};

// ===== SECURITY & COMPLIANCE =====

export const updateSecurityPolicy = async (req, res) => {
  try {
    const policy = req.body;
    await securityService.updatePolicy(policy);
    await auditService.log(req.user.id, 'security_policy_updated', { policy });

    res.json({ message: 'Security policy updated successfully' });
  } catch (error) {
    logger.error('Error updating security policy:', error);
    res.status(500).json({ error: 'Failed to update security policy' });
  }
};

export const getSecurityAuditLogs = async (req, res) => {
  try {
    const { startDate, endDate, severity, limit = 100 } = req.query;

    const logs = await securityService.getAuditLogs({
      startDate,
      endDate,
      severity,
      limit
    });

    res.json({ logs });
  } catch (error) {
    logger.error('Error fetching security audit logs:', error);
    res.status(500).json({ error: 'Failed to fetch security audit logs' });
  }
};

export const manageIPWhitelist = async (req, res) => {
  try {
    const { action, ips } = req.body;

    await securityService.manageIPWhitelist(action, ips);
    await auditService.log(req.user.id, 'ip_whitelist_managed', { action, ips });

    res.json({ message: 'IP whitelist managed successfully' });
  } catch (error) {
    logger.error('Error managing IP whitelist:', error);
    res.status(500).json({ error: 'Failed to manage IP whitelist' });
  }
};

export const configureFirewallRules = async (req, res) => {
  try {
    const { rules } = req.body;

    await securityService.configureFirewallRules(rules);
    await auditService.log(req.user.id, 'firewall_rules_configured', { rules });

    res.json({ message: 'Firewall rules configured successfully' });
  } catch (error) {
    logger.error('Error configuring firewall rules:', error);
    res.status(500).json({ error: 'Failed to configure firewall rules' });
  }
};

export const initiateSecurityScan = async (req, res) => {
  try {
    const { scanType, targets } = req.body;

    const scanId = await securityService.initiateScan(scanType, targets);
    await auditService.log(req.user.id, 'security_scan_initiated', { scanType, targets, scanId });

    res.json({ message: 'Security scan initiated successfully', scanId });
  } catch (error) {
    logger.error('Error initiating security scan:', error);
    res.status(500).json({ error: 'Failed to initiate security scan' });
  }
};

export const getComplianceReports = async (req, res) => {
  try {
    const { standard, startDate, endDate } = req.query;

    const reports = await complianceService.getReports(standard, startDate, endDate);

    res.json({ reports });
  } catch (error) {
    logger.error('Error fetching compliance reports:', error);
    res.status(500).json({ error: 'Failed to fetch compliance reports' });
  }
};

// ===== BILLING & PAYMENT MANAGEMENT =====

export const updateSubscriptionPlan = async (req, res) => {
  try {
    const { planId, features, pricing } = req.body;

    const plan = await billingService.updatePlan(planId, { features, pricing });
    await auditService.log(req.user.id, 'subscription_plan_updated', { planId, features, pricing });

    res.json({ message: 'Subscription plan updated successfully', plan });
  } catch (error) {
    logger.error('Error updating subscription plan:', error);
    res.status(500).json({ error: 'Failed to update subscription plan' });
  }
};

export const getRevenueAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, granularity = 'daily' } = req.query;

    const analytics = await billingService.getRevenueAnalytics({
      startDate,
      endDate,
      granularity
    });

    res.json({ analytics });
  } catch (error) {
    logger.error('Error fetching revenue analytics:', error);
    res.status(500).json({ error: 'Failed to fetch revenue analytics' });
  }
};

export const manageCoupons = async (req, res) => {
  try {
    const { action, couponData } = req.body;

    const result = await billingService.manageCoupons(action, couponData);
    await auditService.log(req.user.id, 'coupons_managed', { action, couponData });

    res.json({ message: 'Coupons managed successfully', result });
  } catch (error) {
    logger.error('Error managing coupons:', error);
    res.status(500).json({ error: 'Failed to manage coupons' });
  }
};

export const processRefunds = async (req, res) => {
  try {
    const { paymentId, amount, reason } = req.body;

    const refund = await billingService.processRefund(paymentId, amount, reason);
    await auditService.log(req.user.id, 'refund_processed', { paymentId, amount, reason });

    res.json({ message: 'Refund processed successfully', refund });
  } catch (error) {
    logger.error('Error processing refund:', error);
    res.status(500).json({ error: 'Failed to process refund' });
  }
};

export const getBillingHistory = async (req, res) => {
  try {
    const { userId, startDate, endDate, limit = 50 } = req.query;

    const history = await billingService.getBillingHistory({
      userId,
      startDate,
      endDate,
      limit
    });

    res.json({ history });
  } catch (error) {
    logger.error('Error fetching billing history:', error);
    res.status(500).json({ error: 'Failed to fetch billing history' });
  }
};

// ===== PLATFORM CONFIGURATION =====

export const updateBrandingSettings = async (req, res) => {
  try {
    const { logo, colors, fonts, customCSS } = req.body;

    const branding = await analyticsService.updateBrandingSettings({
      logo,
      colors,
      fonts,
      customCSS
    });

    await auditService.log(req.user.id, 'branding_updated', { branding });

    res.json({ message: 'Branding settings updated successfully', branding });
  } catch (error) {
    logger.error('Error updating branding settings:', error);
    res.status(500).json({ error: 'Failed to update branding settings' });
  }
};

export const configureGlobalSettings = async (req, res) => {
  try {
    const { timezone, language, currency, features } = req.body;

    const settings = await analyticsService.configureGlobalSettings({
      timezone,
      language,
      currency,
      features
    });

    await auditService.log(req.user.id, 'global_settings_configured', { settings });

    res.json({ message: 'Global settings configured successfully', settings });
  } catch (error) {
    logger.error('Error configuring global settings:', error);
    res.status(500).json({ error: 'Failed to configure global settings' });
  }
};

export const manageEmailTemplates = async (req, res) => {
  try {
    const { templateType, subject, content, variables } = req.body;

    const template = await notificationService.manageEmailTemplate({
      templateType,
      subject,
      content,
      variables
    });

    await auditService.log(req.user.id, 'email_template_managed', { templateType });

    res.json({ message: 'Email template managed successfully', template });
  } catch (error) {
    logger.error('Error managing email template:', error);
    res.status(500).json({ error: 'Failed to manage email template' });
  }
};

export const updateFeatureAvailability = async (req, res) => {
  try {
    const { features } = req.body;

    await analyticsService.updateFeatureAvailability(features);
    await auditService.log(req.user.id, 'feature_availability_updated', { features });

    res.json({ message: 'Feature availability updated successfully' });
  } catch (error) {
    logger.error('Error updating feature availability:', error);
    res.status(500).json({ error: 'Failed to update feature availability' });
  }
};

// ===== AI/ML FEATURES MANAGEMENT =====

export const manageAIFeatures = async (req, res) => {
  try {
    const { featureId, action, configuration } = req.body;

    const result = await analyticsService.manageAIFeature(featureId, action, configuration);
    await auditService.log(req.user.id, 'ai_feature_managed', { featureId, action });

    res.json({ message: 'AI feature managed successfully', result });
  } catch (error) {
    logger.error('Error managing AI feature:', error);
    res.status(500).json({ error: 'Failed to manage AI feature' });
  }
};

export const updateAIModels = async (req, res) => {
  try {
    const { modelId, configuration, trainingData } = req.body;

    const model = await analyticsService.updateAIModel(modelId, configuration, trainingData);
    await auditService.log(req.user.id, 'ai_model_updated', { modelId });

    res.json({ message: 'AI model updated successfully', model });
  } catch (error) {
    logger.error('Error updating AI model:', error);
    res.status(500).json({ error: 'Failed to update AI model' });
  }
};

export const setAIThresholds = async (req, res) => {
  try {
    const { metricId, thresholds } = req.body;

    await analyticsService.setAIThresholds(metricId, thresholds);
    await auditService.log(req.user.id, 'ai_thresholds_set', { metricId, thresholds });

    res.json({ message: 'AI thresholds set successfully' });
  } catch (error) {
    logger.error('Error setting AI thresholds:', error);
    res.status(500).json({ error: 'Failed to set AI thresholds' });
  }
};

export const getAIUsageReports = async (req, res) => {
  try {
    const { startDate, endDate, featureId } = req.query;

    const reports = await analyticsService.getAIUsageReports({
      startDate,
      endDate,
      featureId
    });

    res.json({ reports });
  } catch (error) {
    logger.error('Error fetching AI usage reports:', error);
    res.status(500).json({ error: 'Failed to fetch AI usage reports' });
  }
};

// ===== MONITORING & RESOURCE MANAGEMENT =====

export const getResourceMetrics = async (req, res) => {
  try {
    const { timeRange = '24h', granularity = 'hour' } = req.query;

    const metrics = await analyticsService.getResourceUsageMetrics(timeRange, granularity);

    res.json({ metrics });
  } catch (error) {
    logger.error('Error fetching resource metrics:', error);
    res.status(500).json({ error: 'Failed to fetch resource metrics' });
  }
};

export const configureAlerts = async (req, res) => {
  try {
    const { alertType, conditions, actions } = req.body;

    const alert = await analyticsService.configureAlert(alertType, conditions, actions);
    await auditService.log(req.user.id, 'alert_configured', { alertType });

    res.json({ message: 'Alert configured successfully', alert });
  } catch (error) {
    logger.error('Error configuring alert:', error);
    res.status(500).json({ error: 'Failed to configure alert' });
  }
};

export const getOptimizationRecommendations = async (req, res) => {
  try {
    const recommendations = await analyticsService.getOptimizationRecommendations();

    res.json({ recommendations });
  } catch (error) {
    logger.error('Error fetching optimization recommendations:', error);
    res.status(500).json({ error: 'Failed to fetch optimization recommendations' });
  }
};

export const manualResourceAllocation = async (req, res) => {
  try {
    const { userId, resources } = req.body;

    await analyticsService.allocateResources(userId, resources);
    await auditService.log(req.user.id, 'manual_resource_allocation', { userId, resources });

    res.json({ message: 'Resources allocated successfully' });
  } catch (error) {
    logger.error('Error allocating resources:', error);
    res.status(500).json({ error: 'Failed to allocate resources' });
  }
};

// ===== BACKUP & DISASTER RECOVERY =====

export const initiateGlobalBackup = async (req, res) => {
  try {
    const { backupType = 'full', retention = 30 } = req.body;

    const backup = await backupService.initiateGlobalBackup(backupType, retention);
    await auditService.log(req.user.id, 'global_backup_initiated', { backupId: backup.id });

    res.json({ message: 'Global backup initiated', backupId: backup.id });
  } catch (error) {
    logger.error('Error initiating global backup:', error);
    res.status(500).json({ error: 'Failed to initiate global backup' });
  }
};

export const getBackupStatus = async (req, res) => {
  try {
    const { backupId } = req.query;

    const status = await backupService.getBackupStatus(backupId);

    res.json({ status });
  } catch (error) {
    logger.error('Error fetching backup status:', error);
    res.status(500).json({ error: 'Failed to fetch backup status' });
  }
};

export const configureBackupSettings = async (req, res) => {
  try {
    const { schedule, retention, compression, encryption } = req.body;

    const settings = await backupService.configureSettings({
      schedule,
      retention,
      compression,
      encryption
    });

    await auditService.log(req.user.id, 'backup_settings_configured', { settings });

    res.json({ message: 'Backup settings configured successfully', settings });
  } catch (error) {
    logger.error('Error configuring backup settings:', error);
    res.status(500).json({ error: 'Failed to configure backup settings' });
  }
};

export const restoreFromBackup = async (req, res) => {
  try {
    const { backupId, targetLocation, options } = req.body;

    const restoration = await backupService.restoreFromBackup(backupId, targetLocation, options);
    await auditService.log(req.user.id, 'backup_restored', { backupId, targetLocation });

    res.json({ message: 'Backup restoration initiated', restorationId: restoration.id });
  } catch (error) {
    logger.error('Error restoring from backup:', error);
    res.status(500).json({ error: 'Failed to restore from backup' });
  }
};

// ===== ANALYTICS & REPORTING =====

export const getPlatformReports = async (req, res) => {
  try {
    const { reportType, startDate, endDate, format = 'json' } = req.query;

    const reports = await analyticsService.getPlatformReports({
      reportType,
      startDate,
      endDate,
      format
    });

    res.json({ reports });
  } catch (error) {
    logger.error('Error fetching platform reports:', error);
    res.status(500).json({ error: 'Failed to fetch platform reports' });
  }
};

export const scheduleReports = async (req, res) => {
  try {
    const { reportType, schedule, recipients, format } = req.body;

    const scheduledReport = await analyticsService.scheduleReport({
      reportType,
      schedule,
      recipients,
      format,
      createdBy: req.user.id
    });

    await auditService.log(req.user.id, 'report_scheduled', { reportType, schedule });

    res.json({ message: 'Report scheduled successfully', scheduledReport });
  } catch (error) {
    logger.error('Error scheduling report:', error);
    res.status(500).json({ error: 'Failed to schedule report' });
  }
};

export const getUserGrowthMetrics = async (req, res) => {
  try {
    const { period = '30d', granularity = 'daily' } = req.query;

    const metrics = await analyticsService.getUserGrowthMetrics(period, granularity);

    res.json({ metrics });
  } catch (error) {
    logger.error('Error fetching user growth metrics:', error);
    res.status(500).json({ error: 'Failed to fetch user growth metrics' });
  }
};

export const getFeatureEngagement = async (req, res) => {
  try {
    const { startDate, endDate, featureId } = req.query;

    const engagement = await analyticsService.getFeatureEngagement({
      startDate,
      endDate,
      featureId
    });

    res.json({ engagement });
  } catch (error) {
    logger.error('Error fetching feature engagement:', error);
    res.status(500).json({ error: 'Failed to fetch feature engagement' });
  }
};

// ===== SUPPORT & TICKETING =====

export const getAllTickets = async (req, res) => {
  try {
    const { status, priority, assignedTo, page = 1, limit = 20 } = req.query;

    const query = {};
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;

    const tickets = await SupportTicket.find(query)
      .populate('userId', 'username email')
      .populate('assignedTo', 'username email')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await SupportTicket.countDocuments(query);

    res.json({
      tickets,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    logger.error('Error fetching tickets:', error);
    res.status(500).json({ error: 'Failed to fetch tickets' });
  }
};

export const assignTicket = async (req, res) => {
  try {
    const { id } = req.params;
    const { assignedTo } = req.body;

    const ticket = await SupportTicket.findByIdAndUpdate(
      id,
      {
        assignedTo,
        status: 'assigned',
        assignedAt: new Date()
      },
      { new: true }
    ).populate('assignedTo', 'username email');

    if (!ticket) {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    await auditService.log(req.user.id, 'ticket_assigned', { ticketId: id, assignedTo });

    res.json({ message: 'Ticket assigned successfully', ticket });
  } catch (error) {
    logger.error('Error assigning ticket:', error);
    res.status(500).json({ error: 'Failed to assign ticket' });
  }
};

export const manageKnowledgeBase = async (req, res) => {
  try {
    const { action, articleData } = req.body;

    let result;
    switch (action) {
      case 'create':
        result = await KnowledgeBase.create({
          ...articleData,
          author: req.user.id
        });
        break;
      case 'update':
        result = await KnowledgeBase.findByIdAndUpdate(
          articleData.id,
          articleData,
          { new: true }
        );
        break;
      case 'delete':
        result = await KnowledgeBase.findByIdAndDelete(articleData.id);
        break;
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    await auditService.log(req.user.id, 'knowledge_base_managed', { action, articleId: result?.id });

    res.json({ message: 'Knowledge base managed successfully', result });
  } catch (error) {
    logger.error('Error managing knowledge base:', error);
    res.status(500).json({ error: 'Failed to manage knowledge base' });
  }
};

export const collectFeedback = async (req, res) => {
  try {
    const { feedbackType, content, rating, userId } = req.body;

    const feedback = await analyticsService.collectFeedback({
      feedbackType,
      content,
      rating,
      userId,
      collectedBy: req.user.id
    });

    res.json({ message: 'Feedback collected successfully', feedback });
  } catch (error) {
    logger.error('Error collecting feedback:', error);
    res.status(500).json({ error: 'Failed to collect feedback' });
  }
};

// ===== API MANAGEMENT =====

export const manageAPIKeys = async (req, res) => {
  try {
    const { action, keyData } = req.body;

    const result = await analyticsService.manageAPIKeys(action, keyData);
    await auditService.log(req.user.id, 'api_keys_managed', { action });

    res.json({ message: 'API keys managed successfully', result });
  } catch (error) {
    logger.error('Error managing API keys:', error);
    res.status(500).json({ error: 'Failed to manage API keys' });
  }
};

export const getAPIUsage = async (req, res) => {
  try {
    const { startDate, endDate, keyId } = req.query;

    const usage = await analyticsService.getAPIUsage({
      startDate,
      endDate,
      keyId
    });

    res.json({ usage });
  } catch (error) {
    logger.error('Error fetching API usage:', error);
    res.status(500).json({ error: 'Failed to fetch API usage' });
  }
};

export const configureWebhooks = async (req, res) => {
  try {
    const { url, events, secret } = req.body;

    const webhook = await analyticsService.configureWebhook({
      url,
      events,
      secret,
      createdBy: req.user.id
    });

    await auditService.log(req.user.id, 'webhook_configured', { url, events });

    res.json({ message: 'Webhook configured successfully', webhook });
  } catch (error) {
    logger.error('Error configuring webhook:', error);
    res.status(500).json({ error: 'Failed to configure webhook' });
  }
};

// ===== MIGRATION & IMPORT =====

export const initiateMigration = async (req, res) => {
  try {
    const { sourceType, sourceConfig, targetConfig, options } = req.body;

    const migration = await analyticsService.initiateMigration({
      sourceType,
      sourceConfig,
      targetConfig,
      options,
      initiatedBy: req.user.id
    });

    await auditService.log(req.user.id, 'migration_initiated', { migrationId: migration.id });

    res.json({ message: 'Migration initiated successfully', migrationId: migration.id });
  } catch (error) {
    logger.error('Error initiating migration:', error);
    res.status(500).json({ error: 'Failed to initiate migration' });
  }
};

export const getMigrationStatus = async (req, res) => {
  try {
    const { migrationId } = req.query;

    const status = await analyticsService.getMigrationStatus(migrationId);

    res.json({ status });
  } catch (error) {
    logger.error('Error fetching migration status:', error);
    res.status(500).json({ error: 'Failed to fetch migration status' });
  }
};

// ===== NOTIFICATIONS & ANNOUNCEMENTS =====

export const createAnnouncement = async (req, res) => {
  try {
    const { title, content, type, targetAudience, scheduledAt } = req.body;

    const announcement = await notificationService.createAnnouncement({
      title,
      content,
      type,
      targetAudience,
      scheduledAt,
      createdBy: req.user.id
    });

    await auditService.log(req.user.id, 'announcement_created', { announcementId: announcement.id });

    res.json({ message: 'Announcement created successfully', announcement });
  } catch (error) {
    logger.error('Error creating announcement:', error);
    res.status(500).json({ error: 'Failed to create announcement' });
  }
};

export const configureNotifications = async (req, res) => {
  try {
    const { channels, templates, rules } = req.body;

    const config = await notificationService.configureNotifications({
      channels,
      templates,
      rules
    });

    await auditService.log(req.user.id, 'notifications_configured', { config });

    res.json({ message: 'Notifications configured successfully', config });
  } catch (error) {
    logger.error('Error configuring notifications:', error);
    res.status(500).json({ error: 'Failed to configure notifications' });
  }
};

// ===== VERSION CONTROL & UPDATES =====

export const triggerUpdates = async (req, res) => {
  try {
    const { version, components, rollbackPlan } = req.body;

    const update = await analyticsService.triggerUpdate({
      version,
      components,
      rollbackPlan,
      triggeredBy: req.user.id
    });

    await auditService.log(req.user.id, 'update_triggered', { version, updateId: update.id });

    res.json({ message: 'Update triggered successfully', updateId: update.id });
  } catch (error) {
    logger.error('Error triggering update:', error);
    res.status(500).json({ error: 'Failed to trigger update' });
  }
};

export const manageVersions = async (req, res) => {
  try {
    const { action, versionData } = req.body;

    const result = await analyticsService.manageVersions(action, versionData);
    await auditService.log(req.user.id, 'versions_managed', { action });

    res.json({ message: 'Versions managed successfully', result });
  } catch (error) {
    logger.error('Error managing versions:', error);
    res.status(500).json({ error: 'Failed to manage versions' });
  }
};

// ===== MULTI-TENANCY =====

export const manageTenants = async (req, res) => {
  try {
    const { action, tenantData } = req.body;

    const result = await tenantService.manageTenant(action, tenantData);
    await auditService.log(req.user.id, 'tenant_managed', { action, tenantId: result?.id });

    res.json({ message: 'Tenant managed successfully', result });
  } catch (error) {
    logger.error('Error managing tenant:', error);
    res.status(500).json({ error: 'Failed to manage tenant' });
  }
};

export const getTenantReports = async (req, res) => {
  try {
    const { tenantId, reportType, startDate, endDate } = req.query;

    const reports = await tenantService.getTenantReports({
      tenantId,
      reportType,
      startDate,
      endDate
    });

    res.json({ reports });
  } catch (error) {
    logger.error('Error fetching tenant reports:', error);
    res.status(500).json({ error: 'Failed to fetch tenant reports' });
  }
};

// ===== AUDIT & LOGS =====

export const getAuditLogs = async (req, res) => {
  try {
    const { startDate, endDate, userId, action, limit = 100 } = req.query;

    const logs = await auditService.getLogs({
      startDate,
      endDate,
      userId,
      action,
      limit
    });

    res.json({ logs });
  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    res.status(500).json({ error: 'Failed to fetch audit logs' });
  }
};

export const exportLogs = async (req, res) => {
  try {
    const { format, filters } = req.body;

    const exportResult = await auditService.exportLogs(format, filters);
    await auditService.log(req.user.id, 'logs_exported', { format, filters });

    res.json({ message: 'Logs exported successfully', exportResult });
  } catch (error) {
    logger.error('Error exporting logs:', error);
    res.status(500).json({ error: 'Failed to export logs' });
  }
};

// ===== LEGAL & COMPLIANCE =====

export const updateLegalDocuments = async (req, res) => {
  try {
    const { documentType, content, version } = req.body;

    const document = await complianceService.updateLegalDocument({
      documentType,
      content,
      version,
      updatedBy: req.user.id
    });

    await auditService.log(req.user.id, 'legal_document_updated', { documentType, version });

    res.json({ message: 'Legal document updated successfully', document });
  } catch (error) {
    logger.error('Error updating legal document:', error);
    res.status(500).json({ error: 'Failed to update legal document' });
  }
};

export const handleDataRequests = async (req, res) => {
  try {
    const { requestType, userId, requestData } = req.body;

    const result = await complianceService.handleDataRequest({
      requestType,
      userId,
      requestData,
      handledBy: req.user.id
    });

    await auditService.log(req.user.id, 'data_request_handled', { requestType, userId });

    res.json({ message: 'Data request handled successfully', result });
  } catch (error) {
    logger.error('Error handling data request:', error);
    res.status(500).json({ error: 'Failed to handle data request' });
  }
};