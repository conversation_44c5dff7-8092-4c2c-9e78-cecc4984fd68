import { RBACService } from '../services/rbacService.js';
import { logger } from '../utils/logger.js';

const rbacService = new RBACService();

export const adminMiddleware = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const hasAdminPermission = await rbacService.hasPermission(userId, 'manage_system');

    if (!hasAdminPermission) {
      logger.warn(`Unauthorized admin access attempt by user ${userId}`);
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Add admin context to request
    req.isAdmin = true;
    req.adminPermissions = await rbacService.getRolePermissions('admin');

    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Middleware for specific admin permissions
export const requireAdminPermission = (permission) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;
      const hasPermission = await rbacService.hasPermission(userId, permission);

      if (!hasPermission) {
        logger.warn(`User ${userId} attempted to access ${permission} without authorization`);
        return res.status(403).json({ error: `Required permission: ${permission}` });
      }

      next();
    } catch (error) {
      logger.error(`Permission check error for ${permission}:`, error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
};

// Enhanced admin middleware with role hierarchy
export const requireAdminRole = (minimumRole = 'admin') => {
  const roleHierarchy = {
    'super_admin': 4,
    'admin': 3,
    'moderator': 2,
    'support': 1
  };

  return async (req, res, next) => {
    try {
      const userRole = req.user.role;
      const userRoleLevel = roleHierarchy[userRole] || 0;
      const requiredLevel = roleHierarchy[minimumRole] || 0;

      if (userRoleLevel < requiredLevel) {
        logger.warn(`User ${req.user.id} with role ${userRole} attempted to access ${minimumRole} resource`);
        return res.status(403).json({
          error: 'Insufficient role permissions',
          required: minimumRole,
          current: userRole
        });
      }

      req.adminLevel = userRoleLevel;
      next();
    } catch (error) {
      logger.error('Role check error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
};

// Middleware to check if user can manage other users
export const canManageUsers = async (req, res, next) => {
  try {
    const { id: targetUserId } = req.params;
    const currentUserId = req.user.id;

    // Super admins can manage anyone
    if (req.user.role === 'super_admin') {
      return next();
    }

    // Admins can manage non-admin users
    if (req.user.role === 'admin') {
      const targetUser = await User.findById(targetUserId);
      if (targetUser && targetUser.role !== 'super_admin') {
        return next();
      }
    }

    // Users cannot manage themselves for certain operations
    if (currentUserId === targetUserId) {
      return res.status(403).json({ error: 'Cannot perform this action on yourself' });
    }

    res.status(403).json({ error: 'Insufficient permissions to manage this user' });
  } catch (error) {
    logger.error('User management permission check error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Middleware for IP-based restrictions
export const ipRestrictionMiddleware = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;

    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      logger.warn(`Admin access denied from IP: ${clientIP}`);
      return res.status(403).json({ error: 'Access denied from this IP address' });
    }

    next();
  };
};

// Middleware for time-based restrictions
export const timeRestrictionMiddleware = (allowedHours = { start: 0, end: 24 }) => {
  return (req, res, next) => {
    const currentHour = new Date().getHours();

    if (currentHour < allowedHours.start || currentHour >= allowedHours.end) {
      logger.warn(`Admin access denied outside allowed hours: ${currentHour}`);
      return res.status(403).json({
        error: 'Access denied outside allowed hours',
        allowedHours: `${allowedHours.start}:00 - ${allowedHours.end}:00`
      });
    }

    next();
  };
};