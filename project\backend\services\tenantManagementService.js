import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import { NotificationService } from './notificationService.js';
import { SecurityService } from './securityService.js';
import crypto from 'crypto';

export class TenantManagementService {
  constructor(config = {}) {
    this.config = {
      isolation: config.isolation || 'database', // 'database', 'schema', 'row'
      defaultPlan: config.defaultPlan || 'basic',
      maxTenantsPerPlan: config.maxTenantsPerPlan || {
        basic: 1,
        professional: 5,
        enterprise: 100
      },
      features: config.features || {
        basic: ['core_features'],
        professional: ['core_features', 'advanced_analytics', 'api_access'],
        enterprise: ['core_features', 'advanced_analytics', 'api_access', 'white_label', 'sso']
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.notificationService = new NotificationService();
    this.securityService = new SecurityService();

    // Enhanced tenant management
    this.tenants = new Map();
    this.tenantUsers = new Map();
    this.tenantResources = new Map();

    this.initializeTenantManagement();
  }

  async initializeTenantManagement() {
    try {
      // Load existing tenants (would typically come from database)
      await this.loadTenants();

      logger.info('Enhanced tenant management service initialized successfully');
    } catch (error) {
      logger.error('Error initializing tenant management service:', error);
    }
  }

  async loadTenants() {
    // Mock tenant data - would load from database in production
    const mockTenants = [
      {
        id: 'tenant_1',
        name: 'Acme Corporation',
        subdomain: 'acme',
        plan: 'enterprise',
        status: 'active',
        createdAt: new Date('2024-01-01')
      },
      {
        id: 'tenant_2',
        name: 'Tech Startup Inc',
        subdomain: 'techstartup',
        plan: 'professional',
        status: 'active',
        createdAt: new Date('2024-02-15')
      }
    ];

    for (const tenant of mockTenants) {
      this.tenants.set(tenant.id, tenant);
    }
  }

  async createTenant(tenantData) {
    try {
      // Enhanced validation
      this.validateTenantData(tenantData);

      const tenantId = this.generateTenantId();

      const tenant = {
        id: tenantId,
        name: tenantData.name,
        subdomain: tenantData.subdomain || this.generateSubdomain(tenantData.name),
        plan: tenantData.plan || this.config.defaultPlan,
        status: 'active',
        settings: {
          timezone: tenantData.timezone || 'UTC',
          currency: tenantData.currency || 'USD',
          language: tenantData.language || 'en',
          customization: {
            logo: tenantData.logo,
            primaryColor: tenantData.primaryColor || '#007bff',
            secondaryColor: tenantData.secondaryColor || '#6c757d'
          }
        },
        limits: this.getPlanLimits(tenantData.plan || this.config.defaultPlan),
        features: this.getPlanFeatures(tenantData.plan || this.config.defaultPlan),
        billing: {
          plan: tenantData.plan || this.config.defaultPlan,
          billingCycle: tenantData.billingCycle || 'monthly',
          nextBillingDate: this.calculateNextBillingDate(tenantData.billingCycle || 'monthly')
        },
        security: {
          ssoEnabled: false,
          mfaRequired: false,
          ipWhitelist: [],
          sessionTimeout: 3600 // 1 hour
        },
        metadata: {
          industry: tenantData.industry,
          size: tenantData.size,
          country: tenantData.country,
          customFields: tenantData.customFields || {}
        },
        createdAt: new Date(),
        createdBy: tenantData.createdBy,
        updatedAt: new Date()
      };

      // Validate subdomain uniqueness
      if (this.isSubdomainTaken(tenant.subdomain)) {
        throw new Error(`Subdomain '${tenant.subdomain}' is already taken`);
      }

      // Create tenant isolation (database, schema, etc.)
      await this.createTenantIsolation(tenant);

      // Setup tenant-specific configurations
      await this.configureTenant(tenant);

      // Store tenant
      this.tenants.set(tenantId, tenant);

      // Enhanced audit logging
      await this.auditService.log(
        tenantData.createdBy,
        'tenant_created',
        { tenantId, name: tenant.name, subdomain: tenant.subdomain, plan: tenant.plan },
        'info'
      );

      logger.info(`Tenant created: ${tenant.name} (${tenantId})`);
      return { success: true, tenant };
    } catch (error) {
      logger.error('Error creating tenant:', error);
      return { success: false, error: error.message };
    }
  }

  async updateTenantConfig(tenantId, config) {
    try {
      // Validate configuration changes
      this.validateTenantConfig(config);

      // Apply tenant configuration updates
      const updatedConfig = await this.applyTenantConfig(tenantId, config);

      // Log configuration update
      await this.auditLogService.log({
        action: 'tenant_config_update',
        details: { tenantId, status: 'success' }
      });

      return { success: true, config: updatedConfig };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async getTenantAnalytics(tenantId, period) {
    try {
      // Gather tenant usage statistics
      const usage = await this.collectTenantUsage(tenantId, period);

      // Calculate revenue metrics
      const revenue = await this.calculateTenantRevenue(tenantId, period);

      // Generate comprehensive analytics report
      const report = this.generateAnalyticsReport(usage, revenue);

      return { success: true, analytics: report };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async updateTenantBranding(tenantId, brandingData) {
    try {
      // Validate branding assets
      await this.validateBrandingAssets(brandingData);

      // Apply branding changes
      const branding = await this.applyTenantBranding(tenantId, brandingData);

      // Log branding update
      await this.auditLogService.log({
        action: 'tenant_branding_update',
        details: { tenantId, status: 'success' }
      });

      return { success: true, branding };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  validateTenantData(data) {
    // Implement tenant data validation
    const required = ['name', 'adminEmail', 'plan'];
    for (const field of required) {
      if (!data[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
  }

  async setupTenantEnvironment(data) {
    // Implementation for tenant environment setup
    return {
      id: 'tenant-' + Date.now(),
      name: data.name,
      adminEmail: data.adminEmail,
      plan: data.plan,
      status: 'active'
    };
  }

  async configureTenant(tenant) {
    // Implementation for tenant configuration
  }

  validateTenantConfig(config) {
    // Implementation for config validation
  }

  async applyTenantConfig(tenantId, config) {
    // Implementation for applying tenant configuration
    return config;
  }

  async collectTenantUsage(tenantId, period) {
    // Implementation for collecting usage statistics
    return {};
  }

  async calculateTenantRevenue(tenantId, period) {
    // Implementation for revenue calculation
    return {};
  }

  generateAnalyticsReport(usage, revenue) {
    // Implementation for analytics report generation
    return {
      usage,
      revenue,
      generatedAt: new Date().toISOString()
    };
  }

  async validateBrandingAssets(brandingData) {
    // Implementation for branding validation
  }

  async applyTenantBranding(tenantId, brandingData) {
    // Implementation for applying branding
    return brandingData;
  }

  // ===== ENHANCED TENANT MANAGEMENT METHODS =====

  async getTenant(tenantId) {
    const tenant = this.tenants.get(tenantId);
    if (!tenant) {
      throw new Error(`Tenant ${tenantId} not found`);
    }
    return tenant;
  }

  async getTenantBySubdomain(subdomain) {
    for (const tenant of this.tenants.values()) {
      if (tenant.subdomain === subdomain && tenant.status === 'active') {
        return tenant;
      }
    }
    throw new Error(`Tenant with subdomain '${subdomain}' not found`);
  }

  async listTenants(filters = {}) {
    let tenants = Array.from(this.tenants.values());

    // Apply filters
    if (filters.status) {
      tenants = tenants.filter(t => t.status === filters.status);
    }
    if (filters.plan) {
      tenants = tenants.filter(t => t.plan === filters.plan);
    }
    if (filters.search) {
      const search = filters.search.toLowerCase();
      tenants = tenants.filter(t =>
        t.name.toLowerCase().includes(search) ||
        t.subdomain.toLowerCase().includes(search)
      );
    }

    // Sort
    tenants.sort((a, b) => b.createdAt - a.createdAt);

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    return {
      tenants: tenants.slice(startIndex, endIndex),
      total: tenants.length,
      page,
      limit,
      totalPages: Math.ceil(tenants.length / limit)
    };
  }

  async deleteTenant(tenantId, deletedBy) {
    try {
      const tenant = this.tenants.get(tenantId);
      if (!tenant) {
        throw new Error(`Tenant ${tenantId} not found`);
      }

      // Soft delete - mark as inactive
      tenant.status = 'deleted';
      tenant.deletedAt = new Date();
      tenant.deletedBy = deletedBy;

      // Clean up tenant resources
      await this.cleanupTenantResources(tenantId);

      this.tenants.set(tenantId, tenant);

      await this.auditService.log(
        deletedBy,
        'tenant_deleted',
        { tenantId, name: tenant.name },
        'warning'
      );

      logger.info(`Tenant deleted: ${tenantId}`);
      return { success: true, tenant };
    } catch (error) {
      logger.error('Error deleting tenant:', error);
      return { success: false, error: error.message };
    }
  }

  async addUserToTenant(tenantId, userId, role = 'user') {
    try {
      const tenant = await this.getTenant(tenantId);

      if (!this.tenantUsers.has(tenantId)) {
        this.tenantUsers.set(tenantId, new Map());
      }

      const tenantUsers = this.tenantUsers.get(tenantId);
      tenantUsers.set(userId, {
        userId,
        tenantId,
        role,
        addedAt: new Date(),
        status: 'active'
      });

      await this.auditService.log(
        null,
        'user_added_to_tenant',
        { tenantId, userId, role },
        'info'
      );

      logger.info(`User ${userId} added to tenant ${tenantId} with role ${role}`);
      return { success: true };
    } catch (error) {
      logger.error('Error adding user to tenant:', error);
      return { success: false, error: error.message };
    }
  }

  async removeUserFromTenant(tenantId, userId) {
    try {
      const tenantUsers = this.tenantUsers.get(tenantId);
      if (tenantUsers) {
        tenantUsers.delete(userId);
      }

      await this.auditService.log(
        null,
        'user_removed_from_tenant',
        { tenantId, userId },
        'info'
      );

      logger.info(`User ${userId} removed from tenant ${tenantId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error removing user from tenant:', error);
      return { success: false, error: error.message };
    }
  }

  async getTenantUsers(tenantId) {
    const tenantUsers = this.tenantUsers.get(tenantId);
    return tenantUsers ? Array.from(tenantUsers.values()) : [];
  }

  async getUserTenants(userId) {
    const userTenants = [];

    for (const [tenantId, users] of this.tenantUsers) {
      if (users.has(userId)) {
        const tenant = this.tenants.get(tenantId);
        const userRole = users.get(userId);
        userTenants.push({
          ...tenant,
          userRole: userRole.role
        });
      }
    }

    return userTenants;
  }

  async createTenantIsolation(tenant) {
    // Implementation depends on isolation strategy
    switch (this.config.isolation) {
      case 'database':
        await this.createTenantDatabase(tenant);
        break;
      case 'schema':
        await this.createTenantSchema(tenant);
        break;
      case 'row':
        // Row-level isolation doesn't require separate creation
        break;
    }
  }

  async createTenantDatabase(tenant) {
    // Mock database creation - would use actual database commands
    logger.info(`Creating database for tenant: ${tenant.id}`);
    // await db.createDatabase(`tenant_${tenant.id}`);
  }

  async createTenantSchema(tenant) {
    // Mock schema creation - would use actual database commands
    logger.info(`Creating schema for tenant: ${tenant.id}`);
    // await db.createSchema(`tenant_${tenant.id}`);
  }

  async cleanupTenantResources(tenantId) {
    // Clean up tenant-specific resources
    this.tenantUsers.delete(tenantId);
    this.tenantResources.delete(tenantId);

    // Would also clean up databases, files, etc.
    logger.info(`Cleaned up resources for tenant: ${tenantId}`);
  }

  getPlanLimits(plan) {
    const limits = {
      basic: {
        users: 10,
        storage: 1024 * 1024 * 1024, // 1GB
        apiCalls: 1000,
        domains: 1
      },
      professional: {
        users: 100,
        storage: 10 * 1024 * 1024 * 1024, // 10GB
        apiCalls: 10000,
        domains: 5
      },
      enterprise: {
        users: -1, // unlimited
        storage: -1, // unlimited
        apiCalls: -1, // unlimited
        domains: -1 // unlimited
      }
    };

    return limits[plan] || limits.basic;
  }

  getPlanFeatures(plan) {
    return this.config.features[plan] || this.config.features.basic;
  }

  calculateNextBillingDate(billingCycle) {
    const now = new Date();

    switch (billingCycle) {
      case 'monthly':
        now.setMonth(now.getMonth() + 1);
        break;
      case 'yearly':
        now.setFullYear(now.getFullYear() + 1);
        break;
      default:
        now.setMonth(now.getMonth() + 1);
    }

    return now;
  }

  generateTenantId() {
    return 'tenant_' + crypto.randomBytes(8).toString('hex');
  }

  generateSubdomain(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 20);
  }

  isSubdomainTaken(subdomain) {
    for (const tenant of this.tenants.values()) {
      if (tenant.subdomain === subdomain && tenant.status !== 'deleted') {
        return true;
      }
    }
    return false;
  }

  async getEnhancedTenantAnalytics() {
    const tenants = Array.from(this.tenants.values());
    const activeTenants = tenants.filter(t => t.status === 'active');

    return {
      totalTenants: tenants.length,
      activeTenants: activeTenants.length,
      planDistribution: this.getPlanDistribution(activeTenants),
      growthRate: this.calculateGrowthRate(tenants),
      averageUsersPerTenant: this.calculateAverageUsersPerTenant(),
      topTenantsByUsers: this.getTopTenantsByUsers()
    };
  }

  getPlanDistribution(tenants) {
    const distribution = {};
    tenants.forEach(tenant => {
      distribution[tenant.plan] = (distribution[tenant.plan] || 0) + 1;
    });
    return distribution;
  }

  calculateGrowthRate(tenants) {
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const lastMonth = new Date(thisMonth);
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const thisMonthCount = tenants.filter(t => t.createdAt >= thisMonth).length;
    const lastMonthCount = tenants.filter(t =>
      t.createdAt >= lastMonth && t.createdAt < thisMonth
    ).length;

    return lastMonthCount > 0 ?
      ((thisMonthCount - lastMonthCount) / lastMonthCount) * 100 : 0;
  }

  calculateAverageUsersPerTenant() {
    let totalUsers = 0;
    let tenantCount = 0;

    for (const [tenantId, users] of this.tenantUsers) {
      if (this.tenants.get(tenantId)?.status === 'active') {
        totalUsers += users.size;
        tenantCount++;
      }
    }

    return tenantCount > 0 ? totalUsers / tenantCount : 0;
  }

  getTopTenantsByUsers() {
    const tenantUserCounts = [];

    for (const [tenantId, users] of this.tenantUsers) {
      const tenant = this.tenants.get(tenantId);
      if (tenant?.status === 'active') {
        tenantUserCounts.push({
          tenantId,
          name: tenant.name,
          userCount: users.size
        });
      }
    }

    return tenantUserCounts
      .sort((a, b) => b.userCount - a.userCount)
      .slice(0, 10);
  }
}