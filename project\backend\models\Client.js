import mongoose from 'mongoose';

const clientSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  profile: {
    businessName: String,
    industry: String,
    website: String,
    description: String,
    logo: String,
    contactPerson: {
      name: String,
      email: String,
      phone: String,
      position: String
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      default: 'en'
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'professional', 'enterprise', 'custom'],
      default: 'basic'
    },
    status: {
      type: String,
      enum: ['active', 'suspended', 'cancelled', 'trial'],
      default: 'trial'
    },
    startDate: Date,
    endDate: Date,
    autoRenew: {
      type: Boolean,
      default: true
    },
    billingCycle: {
      type: String,
      enum: ['monthly', 'quarterly', 'yearly'],
      default: 'monthly'
    },
    customFeatures: [String],
    addOns: [{
      name: String,
      price: Number,
      enabled: Boolean,
      addedAt: Date
    }]
  },
  websites: [{
    name: String,
    domain: String,
    subdomain: String,
    platform: String,
    techStack: String,
    status: {
      type: String,
      enum: ['active', 'inactive', 'maintenance', 'suspended'],
      default: 'active'
    },
    ssl: {
      enabled: Boolean,
      provider: String,
      expiryDate: Date,
      autoRenew: Boolean
    },
    cdn: {
      enabled: Boolean,
      provider: String
    },
    backups: {
      enabled: Boolean,
      frequency: String,
      lastBackup: Date,
      retentionDays: Number
    },
    analytics: {
      enabled: Boolean,
      provider: String,
      trackingId: String
    },
    security: {
      firewall: Boolean,
      ddosProtection: Boolean,
      malwareScanning: Boolean,
      lastScan: Date,
      securityScore: Number
    },
    performance: {
      caching: Boolean,
      compression: Boolean,
      optimization: Boolean,
      lastOptimization: Date
    },
    createdAt: Date,
    lastUpdated: Date
  }],
  resources: {
    allocated: {
      storage: Number, // GB
      bandwidth: Number, // GB per month
      domains: Number,
      subdomains: Number,
      emailAccounts: Number,
      databases: Number,
      ftpAccounts: Number
    },
    usage: {
      storage: {
        used: Number,
        files: Number,
        databases: Number,
        emails: Number
      },
      bandwidth: {
        used: Number,
        incoming: Number,
        outgoing: Number
      },
      domains: {
        used: Number,
        active: Number
      },
      emailAccounts: {
        used: Number,
        active: Number
      },
      databases: {
        used: Number,
        active: Number
      }
    },
    limits: {
      maxFileSize: Number, // MB
      maxDatabaseSize: Number, // MB
      maxEmailSize: Number, // MB
      concurrentConnections: Number
    }
  },
  emailAccounts: [{
    email: String,
    password: String, // Encrypted
    quota: Number, // MB
    used: Number, // MB
    forwarders: [String],
    autoResponder: {
      enabled: Boolean,
      subject: String,
      message: String,
      startDate: Date,
      endDate: Date
    },
    filters: [{
      name: String,
      condition: String,
      action: String,
      enabled: Boolean
    }],
    createdAt: Date,
    lastLogin: Date
  }],
  domains: [{
    name: String,
    registrar: String,
    registrationDate: Date,
    expiryDate: Date,
    autoRenew: Boolean,
    nameservers: [String],
    dnsRecords: [{
      type: String,
      name: String,
      value: String,
      ttl: Number,
      priority: Number
    }],
    status: {
      type: String,
      enum: ['active', 'expired', 'suspended', 'pending'],
      default: 'active'
    },
    privacy: Boolean,
    locked: Boolean
  }],
  billing: {
    paymentMethod: {
      type: String,
      last4: String,
      expiryMonth: Number,
      expiryYear: Number,
      brand: String
    },
    billingAddress: {
      name: String,
      company: String,
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    },
    invoices: [{
      invoiceNumber: String,
      amount: Number,
      currency: String,
      status: String,
      dueDate: Date,
      paidDate: Date,
      items: [{
        description: String,
        quantity: Number,
        unitPrice: Number,
        total: Number
      }],
      taxes: [{
        name: String,
        rate: Number,
        amount: Number
      }],
      downloadUrl: String
    }],
    credits: {
      balance: Number,
      currency: String,
      transactions: [{
        type: String,
        amount: Number,
        description: String,
        date: Date
      }]
    }
  },
  support: {
    tickets: [{
      ticketId: String,
      subject: String,
      status: String,
      priority: String,
      category: String,
      assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      createdAt: Date,
      lastUpdate: Date,
      messages: [{
        from: String,
        message: String,
        timestamp: Date,
        attachments: [String]
      }]
    }],
    preferences: {
      preferredLanguage: String,
      contactMethod: String,
      timezone: String
    }
  },
  security: {
    twoFactorAuth: {
      enabled: Boolean,
      method: String,
      backupCodes: [String]
    },
    loginHistory: [{
      ip: String,
      userAgent: String,
      location: String,
      timestamp: Date,
      success: Boolean
    }],
    accessRestrictions: {
      ipWhitelist: [String],
      allowedCountries: [String],
      sessionTimeout: Number
    },
    securityAlerts: {
      loginFromNewDevice: Boolean,
      passwordChange: Boolean,
      billingChange: Boolean,
      resourceUsage: Boolean
    }
  },
  notifications: {
    preferences: {
      email: Boolean,
      sms: Boolean,
      browser: Boolean,
      slack: Boolean
    },
    categories: {
      billing: Boolean,
      security: Boolean,
      maintenance: Boolean,
      updates: Boolean,
      marketing: Boolean
    },
    frequency: {
      immediate: Boolean,
      daily: Boolean,
      weekly: Boolean,
      monthly: Boolean
    }
  },
  analytics: {
    websites: [{
      websiteId: String,
      provider: String,
      trackingId: String,
      enabled: Boolean,
      lastSync: Date
    }],
    reports: {
      traffic: Boolean,
      performance: Boolean,
      security: Boolean,
      uptime: Boolean
    },
    customDashboard: {
      widgets: [String],
      layout: String,
      refreshInterval: Number
    }
  },
  integrations: {
    crm: {
      provider: String,
      apiKey: String,
      enabled: Boolean,
      lastSync: Date
    },
    marketing: {
      provider: String,
      apiKey: String,
      enabled: Boolean,
      lists: [String]
    },
    analytics: {
      provider: String,
      trackingId: String,
      enabled: Boolean
    },
    backup: {
      provider: String,
      config: mongoose.Schema.Types.Mixed,
      enabled: Boolean
    }
  },
  teamMembers: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['viewer', 'editor', 'manager'],
      default: 'viewer'
    },
    permissions: [String],
    invitedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    invitedAt: Date,
    acceptedAt: Date,
    status: {
      type: String,
      enum: ['pending', 'active', 'suspended'],
      default: 'pending'
    }
  }],
  settings: {
    branding: {
      customLogo: String,
      primaryColor: String,
      secondaryColor: String,
      customDomain: String
    },
    features: {
      formBuilder: Boolean,
      emailMarketing: Boolean,
      ecommerce: Boolean,
      multiLanguage: Boolean,
      customCode: Boolean
    },
    privacy: {
      showInDirectory: Boolean,
      allowIndexing: Boolean,
      cookieConsent: Boolean,
      gdprCompliant: Boolean
    }
  },
  managedBy: {
    developer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reseller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    agency: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  metadata: {
    source: String, // How they found us
    referralCode: String,
    tags: [String],
    notes: String,
    customFields: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes
clientSchema.index({ user: 1 });
clientSchema.index({ 'subscription.status': 1 });
clientSchema.index({ 'websites.domain': 1 });
clientSchema.index({ 'managedBy.developer': 1 });
clientSchema.index({ 'managedBy.reseller': 1 });

// Virtual for total storage usage
clientSchema.virtual('totalStorageUsage').get(function() {
  return this.resources.usage.storage.files + 
         this.resources.usage.storage.databases + 
         this.resources.usage.storage.emails;
});

// Virtual for storage usage percentage
clientSchema.virtual('storageUsagePercentage').get(function() {
  const total = this.resources.allocated.storage * 1024; // Convert GB to MB
  const used = this.totalStorageUsage;
  return total > 0 ? (used / total) * 100 : 0;
});

// Methods
clientSchema.methods.addWebsite = function(websiteData) {
  this.websites.push({
    ...websiteData,
    createdAt: new Date(),
    lastUpdated: new Date()
  });
  return this.save();
};

clientSchema.methods.updateResourceUsage = function(resourceType, usage) {
  this.resources.usage[resourceType] = { ...this.resources.usage[resourceType], ...usage };
  return this.save();
};

clientSchema.methods.addTeamMember = function(userId, role, permissions) {
  this.teamMembers.push({
    user: userId,
    role,
    permissions,
    invitedAt: new Date(),
    status: 'pending'
  });
  return this.save();
};

clientSchema.methods.canAccessFeature = function(feature) {
  // Check if feature is included in subscription plan
  const planFeatures = {
    basic: ['websites', 'email', 'support'],
    professional: ['websites', 'email', 'support', 'analytics', 'backups'],
    enterprise: ['websites', 'email', 'support', 'analytics', 'backups', 'advanced_security', 'api_access']
  };
  
  const allowedFeatures = planFeatures[this.subscription.plan] || [];
  return allowedFeatures.includes(feature) || this.subscription.customFeatures.includes(feature);
};

// Static methods
clientSchema.statics.getByDeveloper = function(developerId) {
  return this.find({ 'managedBy.developer': developerId }).populate('user', 'email firstName lastName');
};

clientSchema.statics.getByReseller = function(resellerId) {
  return this.find({ 'managedBy.reseller': resellerId }).populate('user', 'email firstName lastName');
};

clientSchema.statics.getActiveClients = function() {
  return this.find({ 'subscription.status': 'active' });
};

export const Client = mongoose.model('Client', clientSchema);
