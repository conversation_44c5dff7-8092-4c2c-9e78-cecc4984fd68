import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { apiLimiter } from './middleware/rateLimiter.js';
import { ipFilter } from './middleware/ipFilter.js';
import { sessionMiddleware } from './middleware/session.js';
import { analyticsRouter } from './routes/analytics.js';
import { automationRouter } from './routes/automation.js';
import { apiRouter } from './routes/api.js';
import developerRoutes from './routes/developerRoutes.js';
import clientRoutes from './routes/clientRoutes.js';
import { errorHandler } from './middleware/errorHandler.js';
import { logger } from './utils/logger.js';
import { cacheMiddleware } from './utils/redis.js';

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:5173',
    credentials: true
  }
});

// Security middleware
app.use(helmet()); // Security headers
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(express.json()); // Parse JSON bodies
app.use(compression()); // Compress responses
app.use(morgan('combined')); // Request logging
app.use(sessionMiddleware);

// IP filtering for admin routes
app.use('/api/admin', ipFilter(process.env.ALLOWED_IPS?.split(',') || []));

// Rate limiting
app.use('/api/', apiLimiter);

// Cache frequently accessed routes
app.use('/api/analytics', cacheMiddleware(300)); // Cache for 5 minutes

// Routes
app.use('/api/analytics', analyticsRouter);
app.use('/api/automation', automationRouter);
app.use('/api/developer', developerRoutes);
app.use('/api/client', clientRoutes);
app.use('/api', apiRouter);

// WebSocket setup for real-time updates
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on('subscribe:metrics', (resourceId) => {
    socket.join(`metrics:${resourceId}`);
  });

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Error handling
app.use(errorHandler);

// Start server
const port = process.env.PORT || 3000;
httpServer.listen(port, () => {
  logger.info(`Server running on port ${port}`);
});

export default app;