import mongoose from 'mongoose';

const announcementSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    maxlength: 200,
    trim: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 5000
  },
  excerpt: {
    type: String,
    maxlength: 300
  },
  type: {
    type: String,
    required: true,
    enum: ['info', 'warning', 'error', 'success', 'maintenance', 'feature', 'security'],
    default: 'info'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent', 'critical'],
    default: 'normal'
  },
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'published', 'expired', 'cancelled'],
    default: 'draft'
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'internal', 'customer_only', 'admin_only'],
    default: 'public'
  },
  targetAudience: {
    type: String,
    enum: ['all', 'admins', 'users', 'customers', 'developers', 'resellers', 'specific_roles', 'specific_users'],
    default: 'all'
  },
  targetRoles: [String], // Specific roles when targetAudience is 'specific_roles'
  targetUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }], // Specific users when targetAudience is 'specific_users'
  channels: [{
    type: String,
    enum: ['dashboard', 'email', 'sms', 'push', 'banner', 'modal', 'notification_center'],
    default: 'dashboard'
  }],
  scheduling: {
    publishAt: {
      type: Date,
      default: Date.now
    },
    expiresAt: Date,
    timezone: {
      type: String,
      default: 'UTC'
    },
    recurring: {
      enabled: {
        type: Boolean,
        default: false
      },
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'yearly']
      },
      interval: Number, // Every X days/weeks/months/years
      endDate: Date,
      daysOfWeek: [Number], // 0-6, Sunday = 0
      dayOfMonth: Number // 1-31
    }
  },
  display: {
    showOnLogin: {
      type: Boolean,
      default: false
    },
    showOnDashboard: {
      type: Boolean,
      default: true
    },
    dismissible: {
      type: Boolean,
      default: true
    },
    autoHide: {
      enabled: {
        type: Boolean,
        default: false
      },
      duration: Number // seconds
    },
    position: {
      type: String,
      enum: ['top', 'bottom', 'center', 'top-right', 'top-left', 'bottom-right', 'bottom-left'],
      default: 'top'
    },
    style: {
      backgroundColor: String,
      textColor: String,
      borderColor: String,
      icon: String,
      animation: {
        type: String,
        enum: ['none', 'fade', 'slide', 'bounce', 'pulse'],
        default: 'fade'
      }
    }
  },
  content_formatting: {
    allowHtml: {
      type: Boolean,
      default: true
    },
    allowMarkdown: {
      type: Boolean,
      default: false
    },
    processedContent: String, // HTML/Markdown processed content
    attachments: [{
      filename: String,
      originalName: String,
      mimeType: String,
      size: Number,
      path: String,
      description: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }],
    links: [{
      text: String,
      url: String,
      target: {
        type: String,
        enum: ['_self', '_blank'],
        default: '_self'
      }
    }]
  },
  actions: [{
    label: String,
    action: {
      type: String,
      enum: ['link', 'dismiss', 'callback', 'download']
    },
    value: String, // URL for link, callback function name, etc.
    style: {
      type: String,
      enum: ['primary', 'secondary', 'success', 'warning', 'danger'],
      default: 'primary'
    },
    icon: String
  }],
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    uniqueViews: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    dismissals: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    engagementRate: {
      type: Number,
      default: 0
    },
    lastViewed: Date,
    viewsByChannel: {
      dashboard: { type: Number, default: 0 },
      email: { type: Number, default: 0 },
      sms: { type: Number, default: 0 },
      push: { type: Number, default: 0 },
      banner: { type: Number, default: 0 },
      modal: { type: Number, default: 0 }
    }
  },
  delivery: {
    totalRecipients: {
      type: Number,
      default: 0
    },
    deliveredCount: {
      type: Number,
      default: 0
    },
    failedCount: {
      type: Number,
      default: 0
    },
    deliveryStatus: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'failed', 'partial'],
      default: 'pending'
    },
    deliveryStarted: Date,
    deliveryCompleted: Date,
    deliveryErrors: [String],
    channelDelivery: {
      dashboard: {
        status: { type: String, enum: ['pending', 'delivered', 'failed'], default: 'pending' },
        deliveredAt: Date,
        error: String
      },
      email: {
        status: { type: String, enum: ['pending', 'delivered', 'failed'], default: 'pending' },
        deliveredAt: Date,
        error: String,
        messageId: String
      },
      sms: {
        status: { type: String, enum: ['pending', 'delivered', 'failed'], default: 'pending' },
        deliveredAt: Date,
        error: String,
        messageId: String
      },
      push: {
        status: { type: String, enum: ['pending', 'delivered', 'failed'], default: 'pending' },
        deliveredAt: Date,
        error: String,
        messageId: String
      }
    }
  },
  userInteractions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    action: {
      type: String,
      enum: ['viewed', 'clicked', 'dismissed', 'shared']
    },
    channel: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  localization: [{
    language: String,
    title: String,
    content: String,
    excerpt: String
  }],
  approval: {
    required: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectedAt: Date,
    rejectionReason: String,
    comments: [{
      author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      content: String,
      createdAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  tags: [String],
  metadata: {
    source: String,
    campaign: String,
    version: {
      type: String,
      default: '1.0'
    },
    customFields: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes
announcementSchema.index({ status: 1, 'scheduling.publishAt': 1 });
announcementSchema.index({ targetAudience: 1, status: 1 });
announcementSchema.index({ type: 1, priority: 1 });
announcementSchema.index({ 'scheduling.expiresAt': 1 });
announcementSchema.index({ createdBy: 1, createdAt: -1 });
announcementSchema.index({ tags: 1 });
announcementSchema.index({ 'analytics.views': -1 });
announcementSchema.index({ channels: 1, status: 1 });

// Virtual for checking if announcement is active
announcementSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'published' && 
         this.scheduling.publishAt <= now && 
         (!this.scheduling.expiresAt || this.scheduling.expiresAt > now);
});

// Virtual for checking if announcement is expired
announcementSchema.virtual('isExpired').get(function() {
  return this.scheduling.expiresAt && this.scheduling.expiresAt < new Date();
});

// Methods
announcementSchema.methods.recordView = function(userId, channel = 'dashboard', isUnique = false) {
  this.analytics.views += 1;
  this.analytics.viewsByChannel[channel] += 1;
  
  if (isUnique) {
    this.analytics.uniqueViews += 1;
  }
  
  this.analytics.lastViewed = new Date();
  
  // Record user interaction
  this.userInteractions.push({
    user: userId,
    action: 'viewed',
    channel,
    timestamp: new Date()
  });
  
  this.updateEngagementRate();
  return this.save();
};

announcementSchema.methods.recordClick = function(userId, channel = 'dashboard') {
  this.analytics.clicks += 1;
  
  this.userInteractions.push({
    user: userId,
    action: 'clicked',
    channel,
    timestamp: new Date()
  });
  
  this.updateEngagementRate();
  return this.save();
};

announcementSchema.methods.recordDismissal = function(userId, channel = 'dashboard') {
  this.analytics.dismissals += 1;
  
  this.userInteractions.push({
    user: userId,
    action: 'dismissed',
    channel,
    timestamp: new Date()
  });
  
  this.updateEngagementRate();
  return this.save();
};

announcementSchema.methods.updateEngagementRate = function() {
  if (this.analytics.views > 0) {
    this.analytics.engagementRate = 
      ((this.analytics.clicks + this.analytics.shares) / this.analytics.views) * 100;
  }
};

announcementSchema.methods.publish = function(publishedBy) {
  this.status = 'published';
  this.updatedBy = publishedBy;
  
  if (!this.scheduling.publishAt || this.scheduling.publishAt < new Date()) {
    this.scheduling.publishAt = new Date();
  }
  
  return this.save();
};

announcementSchema.methods.schedule = function(publishAt, expiresAt = null) {
  this.status = 'scheduled';
  this.scheduling.publishAt = publishAt;
  if (expiresAt) {
    this.scheduling.expiresAt = expiresAt;
  }
  return this.save();
};

announcementSchema.methods.approve = function(approvedBy) {
  this.approval.status = 'approved';
  this.approval.approvedBy = approvedBy;
  this.approval.approvedAt = new Date();
  return this.save();
};

announcementSchema.methods.reject = function(rejectedBy, reason) {
  this.approval.status = 'rejected';
  this.approval.rejectedBy = rejectedBy;
  this.approval.rejectedAt = new Date();
  this.approval.rejectionReason = reason;
  return this.save();
};

// Static methods
announcementSchema.statics.getActiveAnnouncements = function(targetAudience = 'all', channel = null) {
  const now = new Date();
  const query = {
    status: 'published',
    'scheduling.publishAt': { $lte: now },
    $or: [
      { 'scheduling.expiresAt': { $exists: false } },
      { 'scheduling.expiresAt': { $gt: now } }
    ]
  };
  
  if (targetAudience !== 'all') {
    query.targetAudience = { $in: [targetAudience, 'all'] };
  }
  
  if (channel) {
    query.channels = channel;
  }
  
  return this.find(query)
    .populate('createdBy', 'username email')
    .sort({ priority: -1, 'scheduling.publishAt': -1 });
};

announcementSchema.statics.getScheduledAnnouncements = function() {
  return this.find({
    status: 'scheduled',
    'scheduling.publishAt': { $lte: new Date() }
  }).populate('createdBy', 'username email');
};

announcementSchema.statics.getExpiredAnnouncements = function() {
  return this.find({
    status: 'published',
    'scheduling.expiresAt': { $lt: new Date() }
  });
};

announcementSchema.statics.getAnnouncementStatistics = function(timeRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);
  
  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalViews: { $sum: '$analytics.views' },
        totalClicks: { $sum: '$analytics.clicks' },
        avgEngagement: { $avg: '$analytics.engagementRate' }
      }
    }
  ]);
};

// Pre-save middleware
announcementSchema.pre('save', function(next) {
  // Auto-expire announcements
  if (this.isExpired && this.status === 'published') {
    this.status = 'expired';
  }
  
  // Generate excerpt if not provided
  if (!this.excerpt && this.content) {
    this.excerpt = this.content.substring(0, 297) + '...';
  }
  
  // Process content if HTML/Markdown is enabled
  if (this.content_formatting.allowHtml || this.content_formatting.allowMarkdown) {
    this.content_formatting.processedContent = this.content; // Would process with actual parser
  }
  
  next();
});

export const Announcement = mongoose.model('Announcement', announcementSchema);
