import mongoose from 'mongoose';

const securityPolicySchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['global', 'tenant', 'user'],
    default: 'global'
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  passwordPolicy: {
    minLength: {
      type: Number,
      default: 8
    },
    requireUppercase: {
      type: Boolean,
      default: true
    },
    requireLowercase: {
      type: Boolean,
      default: true
    },
    requireNumbers: {
      type: Boolean,
      default: true
    },
    requireSpecialChars: {
      type: Boolean,
      default: true
    },
    maxAge: {
      type: Number,
      default: 90 // days
    },
    preventReuse: {
      type: Number,
      default: 5 // last N passwords
    },
    lockoutThreshold: {
      type: Number,
      default: 5 // failed attempts
    },
    lockoutDuration: {
      type: Number,
      default: 30 // minutes
    }
  },
  sessionPolicy: {
    maxDuration: {
      type: Number,
      default: 24 * 60 * 60 * 1000 // 24 hours in ms
    },
    idleTimeout: {
      type: Number,
      default: 2 * 60 * 60 * 1000 // 2 hours in ms
    },
    maxConcurrentSessions: {
      type: Number,
      default: 3
    },
    requireSecureCookies: {
      type: Boolean,
      default: true
    }
  },
  mfaPolicy: {
    required: {
      type: Boolean,
      default: false
    },
    requiredForRoles: [{
      type: String,
      enum: ['admin', 'super_admin', 'moderator']
    }],
    allowedMethods: [{
      type: String,
      enum: ['totp', 'sms', 'email', 'backup_codes'],
      default: ['totp']
    }],
    backupCodesCount: {
      type: Number,
      default: 10
    }
  },
  accessPolicy: {
    allowedIPs: [{
      type: String
    }],
    blockedIPs: [{
      type: String
    }],
    allowedCountries: [{
      type: String
    }],
    blockedCountries: [{
      type: String
    }],
    timeRestrictions: {
      enabled: {
        type: Boolean,
        default: false
      },
      allowedHours: {
        start: {
          type: Number,
          default: 0
        },
        end: {
          type: Number,
          default: 24
        }
      },
      timezone: {
        type: String,
        default: 'UTC'
      }
    }
  },
  auditPolicy: {
    logLevel: {
      type: String,
      enum: ['minimal', 'standard', 'detailed', 'verbose'],
      default: 'standard'
    },
    retentionPeriod: {
      type: Number,
      default: 365 // days
    },
    realTimeAlerts: {
      type: Boolean,
      default: true
    },
    alertThresholds: {
      failedLogins: {
        type: Number,
        default: 10
      },
      suspiciousActivity: {
        type: Number,
        default: 5
      }
    }
  },
  encryptionPolicy: {
    algorithm: {
      type: String,
      default: 'AES-256-GCM'
    },
    keyRotationInterval: {
      type: Number,
      default: 90 // days
    },
    requireEncryptionAtRest: {
      type: Boolean,
      default: true
    },
    requireEncryptionInTransit: {
      type: Boolean,
      default: true
    }
  },
  complianceSettings: {
    gdprCompliant: {
      type: Boolean,
      default: false
    },
    hipaaCompliant: {
      type: Boolean,
      default: false
    },
    sox404Compliant: {
      type: Boolean,
      default: false
    },
    dataRetentionPeriod: {
      type: Number,
      default: 2555 // 7 years in days
    },
    rightToBeForgettenEnabled: {
      type: Boolean,
      default: true
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  version: {
    type: Number,
    default: 1
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  appliedTo: [{
    type: {
      type: String,
      enum: ['global', 'tenant', 'user', 'role']
    },
    targetId: {
      type: mongoose.Schema.Types.ObjectId
    }
  }],
  effectiveDate: {
    type: Date,
    default: Date.now
  },
  expirationDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes
securityPolicySchema.index({ type: 1, isActive: 1 });
securityPolicySchema.index({ createdBy: 1 });
securityPolicySchema.index({ effectiveDate: 1, expirationDate: 1 });

// Methods
securityPolicySchema.methods.isEffective = function() {
  const now = new Date();
  return this.isActive && 
         this.effectiveDate <= now && 
         (!this.expirationDate || this.expirationDate > now);
};

securityPolicySchema.methods.createNewVersion = function(updates, updatedBy) {
  const newPolicy = new this.constructor({
    ...this.toObject(),
    _id: undefined,
    version: this.version + 1,
    updatedBy,
    ...updates,
    createdAt: new Date(),
    updatedAt: new Date()
  });
  
  return newPolicy;
};

// Static methods
securityPolicySchema.statics.getActivePolicy = function(type = 'global') {
  return this.findOne({
    type,
    isActive: true,
    effectiveDate: { $lte: new Date() },
    $or: [
      { expirationDate: { $exists: false } },
      { expirationDate: { $gt: new Date() } }
    ]
  }).sort({ version: -1 });
};

securityPolicySchema.statics.getPolicyHistory = function(type = 'global') {
  return this.find({ type }).sort({ version: -1 });
};

// Pre-save middleware
securityPolicySchema.pre('save', function(next) {
  if (this.isNew) {
    this.effectiveDate = this.effectiveDate || new Date();
  }
  next();
});

export const SecurityPolicy = mongoose.model('SecurityPolicy', securityPolicySchema);
