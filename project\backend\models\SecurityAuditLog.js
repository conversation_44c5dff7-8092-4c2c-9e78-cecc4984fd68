import mongoose from 'mongoose';

const securityAuditLogSchema = new mongoose.Schema({
  eventType: {
    type: String,
    required: true,
    enum: [
      'login_success',
      'login_failure',
      'logout',
      'password_change',
      'password_reset',
      'mfa_enabled',
      'mfa_disabled',
      'mfa_verification_success',
      'mfa_verification_failure',
      'account_locked',
      'account_unlocked',
      'permission_granted',
      'permission_revoked',
      'role_assigned',
      'role_removed',
      'policy_updated',
      'security_scan_initiated',
      'security_scan_completed',
      'vulnerability_detected',
      'malware_detected',
      'ddos_attempt',
      'brute_force_detected',
      'ip_blocked',
      'ip_unblocked',
      'firewall_rule_added',
      'firewall_rule_removed',
      'encryption_key_rotated',
      'backup_created',
      'backup_restored',
      'data_export',
      'data_deletion',
      'compliance_violation',
      'suspicious_activity',
      'unauthorized_access_attempt',
      'privilege_escalation_attempt',
      'data_breach_detected',
      'system_compromise_detected'
    ]
  },
  severity: {
    type: String,
    required: true,
    enum: ['info', 'warning', 'error', 'critical'],
    default: 'info'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  sessionId: {
    type: String
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  location: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  source: {
    type: String,
    required: true,
    default: 'system'
  },
  resource: {
    type: String // What resource was accessed/modified
  },
  action: {
    type: String // What action was performed
  },
  outcome: {
    type: String,
    enum: ['success', 'failure', 'blocked', 'pending'],
    default: 'success'
  },
  riskScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  data: {
    type: mongoose.Schema.Types.Mixed // Additional event-specific data
  },
  metadata: {
    requestId: String,
    correlationId: String,
    traceId: String,
    spanId: String
  },
  tags: [{
    type: String
  }],
  alertTriggered: {
    type: Boolean,
    default: false
  },
  alertRules: [{
    ruleId: String,
    ruleName: String,
    triggered: Boolean
  }],
  investigation: {
    status: {
      type: String,
      enum: ['none', 'pending', 'in_progress', 'resolved', 'false_positive'],
      default: 'none'
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: [{
      author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      content: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }],
    resolution: String,
    resolvedAt: Date
  },
  relatedEvents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SecurityAuditLog'
  }],
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  processed: {
    type: Boolean,
    default: false
  },
  archived: {
    type: Boolean,
    default: false
  },
  retentionDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for performance
securityAuditLogSchema.index({ eventType: 1, timestamp: -1 });
securityAuditLogSchema.index({ severity: 1, timestamp: -1 });
securityAuditLogSchema.index({ userId: 1, timestamp: -1 });
securityAuditLogSchema.index({ ipAddress: 1, timestamp: -1 });
securityAuditLogSchema.index({ sessionId: 1 });
securityAuditLogSchema.index({ timestamp: -1 });
securityAuditLogSchema.index({ alertTriggered: 1, timestamp: -1 });
securityAuditLogSchema.index({ 'investigation.status': 1 });
securityAuditLogSchema.index({ archived: 1, retentionDate: 1 });
securityAuditLogSchema.index({ riskScore: -1, timestamp: -1 });

// Compound indexes
securityAuditLogSchema.index({ eventType: 1, severity: 1, timestamp: -1 });
securityAuditLogSchema.index({ userId: 1, eventType: 1, timestamp: -1 });

// Methods
securityAuditLogSchema.methods.calculateRiskScore = function() {
  let score = 0;
  
  // Base score by event type
  const eventRiskScores = {
    'login_failure': 10,
    'brute_force_detected': 80,
    'ddos_attempt': 70,
    'malware_detected': 90,
    'unauthorized_access_attempt': 85,
    'privilege_escalation_attempt': 95,
    'data_breach_detected': 100,
    'system_compromise_detected': 100,
    'suspicious_activity': 60,
    'account_locked': 30,
    'vulnerability_detected': 50
  };
  
  score += eventRiskScores[this.eventType] || 5;
  
  // Severity multiplier
  const severityMultipliers = {
    'info': 1,
    'warning': 1.5,
    'error': 2,
    'critical': 3
  };
  
  score *= severityMultipliers[this.severity] || 1;
  
  // Time-based factors (recent events are riskier)
  const hoursSinceEvent = (Date.now() - this.timestamp.getTime()) / (1000 * 60 * 60);
  if (hoursSinceEvent < 1) score *= 1.5;
  else if (hoursSinceEvent < 24) score *= 1.2;
  
  // Cap at 100
  this.riskScore = Math.min(Math.round(score), 100);
  return this.riskScore;
};

securityAuditLogSchema.methods.shouldTriggerAlert = function() {
  const alertThresholds = {
    'brute_force_detected': 50,
    'ddos_attempt': 60,
    'malware_detected': 70,
    'unauthorized_access_attempt': 65,
    'privilege_escalation_attempt': 80,
    'data_breach_detected': 90,
    'system_compromise_detected': 90,
    'suspicious_activity': 40
  };
  
  return this.riskScore >= (alertThresholds[this.eventType] || 75);
};

securityAuditLogSchema.methods.addInvestigationNote = function(authorId, content) {
  this.investigation.notes.push({
    author: authorId,
    content,
    timestamp: new Date()
  });
  return this.save();
};

securityAuditLogSchema.methods.resolveInvestigation = function(resolution, resolvedBy) {
  this.investigation.status = 'resolved';
  this.investigation.resolution = resolution;
  this.investigation.resolvedAt = new Date();
  this.investigation.assignedTo = resolvedBy;
  return this.save();
};

// Static methods
securityAuditLogSchema.statics.getHighRiskEvents = function(timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  return this.find({
    timestamp: { $gte: startTime },
    riskScore: { $gte: 70 }
  }).sort({ riskScore: -1, timestamp: -1 });
};

securityAuditLogSchema.statics.getEventsByUser = function(userId, limit = 100) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('userId', 'username email');
};

securityAuditLogSchema.statics.getSecurityMetrics = function(timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  
  return this.aggregate([
    { $match: { timestamp: { $gte: startTime } } },
    {
      $group: {
        _id: {
          eventType: '$eventType',
          severity: '$severity'
        },
        count: { $sum: 1 },
        avgRiskScore: { $avg: '$riskScore' },
        maxRiskScore: { $max: '$riskScore' }
      }
    },
    {
      $group: {
        _id: '$_id.eventType',
        totalCount: { $sum: '$count' },
        severityBreakdown: {
          $push: {
            severity: '$_id.severity',
            count: '$count',
            avgRiskScore: '$avgRiskScore',
            maxRiskScore: '$maxRiskScore'
          }
        }
      }
    },
    { $sort: { totalCount: -1 } }
  ]);
};

// Pre-save middleware
securityAuditLogSchema.pre('save', function(next) {
  if (this.isNew) {
    this.calculateRiskScore();
    
    if (this.shouldTriggerAlert()) {
      this.alertTriggered = true;
    }
    
    // Set retention date based on severity
    const retentionDays = {
      'info': 90,
      'warning': 180,
      'error': 365,
      'critical': 2555 // 7 years
    };
    
    const retention = retentionDays[this.severity] || 365;
    this.retentionDate = new Date(Date.now() + retention * 24 * 60 * 60 * 1000);
  }
  next();
});

export const SecurityAuditLog = mongoose.model('SecurityAuditLog', securityAuditLogSchema);
