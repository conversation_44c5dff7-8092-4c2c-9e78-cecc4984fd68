import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import WebSocket from 'ws';
import twilio from 'twilio';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';

const readFile = promisify(fs.readFile);

export class NotificationService extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = {
      email: {
        provider: config.email?.provider || 'smtp',
        smtp: {
          host: config.email?.smtp?.host || process.env.SMTP_HOST,
          port: config.email?.smtp?.port || process.env.SMTP_PORT || 587,
          secure: config.email?.smtp?.secure || false,
          auth: {
            user: config.email?.smtp?.user || process.env.SMTP_USER,
            pass: config.email?.smtp?.pass || process.env.SMTP_PASS
          }
        },
        from: config.email?.from || process.env.EMAIL_FROM || '<EMAIL>'
      },
      sms: {
        enabled: config.sms?.enabled || false,
        provider: config.sms?.provider || 'twilio',
        accountSid: config.sms?.accountSid || process.env.TWILIO_ACCOUNT_SID,
        authToken: config.sms?.authToken || process.env.TWILIO_AUTH_TOKEN,
        phoneNumber: config.sms?.phoneNumber || process.env.TWILIO_FROM
      },
      ...config
    };

    this.clients = new Map();
    this.rules = new Map();
    this.healthMetrics = new Map();
    this.auditService = new AuditLogService();

    // Initialize email transport
    this.transporter = nodemailer.createTransport(this.config.email.smtp);

    // Initialize SMS client
    if (this.config.sms.enabled && this.config.sms.accountSid) {
      this.smsClient = twilio(
        this.config.sms.accountSid,
        this.config.sms.authToken
      );
    }

    this.templateDir = path.join(process.cwd(), 'templates', 'email');
    this.ensureTemplateDirectory();
    this.initializeWebSocket();
  }

  async ensureTemplateDirectory() {
    if (!fs.existsSync(this.templateDir)) {
      fs.mkdirSync(this.templateDir, { recursive: true });
    }
  }

  async loadTemplate(templateName) {
    try {
      const templatePath = path.join(this.templateDir, `${templateName}.html`);
      return await readFile(templatePath, 'utf8');
    } catch (error) {
      console.error(`Failed to load template ${templateName}:`, error);
      throw new Error('Template not found');
    }
  }

  async sendEmail(to, subject, templateName, data) {
    try {
      let template = await this.loadTemplate(templateName);

      // Replace placeholders in template with actual data
      Object.entries(data).forEach(([key, value]) => {
        template = template.replace(new RegExp(`{{${key}}}`, 'g'), value);
      });

      const mailOptions = {
        from: this.config.smtp.from,
        to,
        subject,
        html: template
      };

      await this.transporter.sendMail(mailOptions);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error('Email sending failed');
    }
  }

  initializeWebSocket() {
    this.wss = new WebSocket.Server({ noServer: true });

    this.wss.on('connection', (ws, request) => {
      const userId = request.session?.userId;
      if (userId) {
        this.clients.set(userId, ws);

        ws.on('message', (message) => {
          try {
            const data = JSON.parse(message);
            if (data.type === 'subscribe') {
              this.handleSubscription(userId, data.channels);
            }
          } catch (error) {
            console.error('WebSocket message handling error:', error);
          }
        });

        ws.on('close', () => {
          this.clients.delete(userId);
        });
      }
    });
  }

  async sendSMS(to, message) {
    if (!this.smsClient) {
      throw new Error('SMS service not configured');
    }

    try {
      await this.smsClient.messages.create({
        body: message,
        to,
        from: this.config.sms.phoneNumber
      });
      return true;
    } catch (error) {
      console.error('Failed to send SMS:', error);
      throw new Error('SMS sending failed');
    }
  }

  sendRealTimeNotification(userId, notification) {
    const client = this.clients.get(userId);
    if (client?.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(notification));
    }
  }

  addNotificationRule(ruleId, condition, actions) {
    this.rules.set(ruleId, { condition, actions });
  }

  removeNotificationRule(ruleId) {
    this.rules.delete(ruleId);
  }

  async processNotification(notification) {
    for (const [ruleId, rule] of this.rules) {
      if (await rule.condition(notification)) {
        await Promise.all(rule.actions.map(action => action(notification)));
      }
    }
  }

  updateHealthMetric(metricName, value, threshold) {
    const metric = {
      value,
      threshold,
      timestamp: new Date().toISOString()
    };

    this.healthMetrics.set(metricName, metric);
    this.checkHealthMetric(metricName, metric);
  }

  async checkHealthMetric(metricName, metric) {
    if (metric.value > metric.threshold) {
      const alert = {
        type: 'system_health',
        severity: 'warning',
        metric: metricName,
        value: metric.value,
        threshold: metric.threshold,
        timestamp: metric.timestamp
      };

      await this.processNotification(alert);
    }
  }

  async sendSystemAlert(to, alertType, data) {
    const templates = {
      'system_error': 'system-error-alert',
      'performance_warning': 'performance-warning',
      'security_alert': 'security-alert',
      'backup_status': 'backup-status',
      'system_health': 'system-health-alert'
    };

    const templateName = templates[alertType] || 'general-alert';
    const subject = `System Alert: ${alertType.replace('_', ' ').toUpperCase()}`;

    const notification = {
      type: alertType,
      data,
      timestamp: new Date().toISOString()
    };

    await this.processNotification(notification);

    if (data.severity === 'critical' && this.config.sms?.enabled) {
      await this.sendSMS(to, `CRITICAL ALERT: ${subject}\n${data.message}`);
    }

    return this.sendEmail(to, subject, templateName, data);
  }

  async sendUserNotification(user, type, data) {
    if (!user.notificationPreferences?.[type]) {
      return false; // User has disabled this type of notification
    }

    const templates = {
      'account_update': 'account-update',
      'security_event': 'security-notification',
      'task_complete': 'task-completion',
      'system_maintenance': 'maintenance-notice'
    };

    const templateName = templates[type] || 'general-notification';
    const subject = `${type.replace('_', ' ').toUpperCase()} Notification`;

    const notification = {
      type,
      data,
      timestamp: new Date().toISOString()
    };

    this.sendRealTimeNotification(user.id, notification);
    return this.sendEmail(user.email, subject, templateName, data);
  }

  async updateUserPreferences(userId, preferences) {
    // This method would typically interact with a database
    // to update user notification preferences
    try {
      // Example: await db.users.update(userId, { notificationPreferences: preferences });
      return true;
    } catch (error) {
      logger.error('Failed to update notification preferences:', error);
      throw new Error('Failed to update notification preferences');
    }
  }

  // ===== ADMIN-SPECIFIC METHODS =====

  async createAnnouncement(announcementData) {
    try {
      const announcement = {
        id: this.generateId(),
        title: announcementData.title,
        content: announcementData.content,
        type: announcementData.type || 'info', // info, warning, error, success
        targetAudience: announcementData.targetAudience || 'all', // all, admins, users, role:specific
        scheduledAt: announcementData.scheduledAt || new Date(),
        expiresAt: announcementData.expiresAt,
        channels: announcementData.channels || ['dashboard'], // dashboard, email, sms, push
        isActive: true,
        createdBy: announcementData.createdBy,
        createdAt: new Date()
      };

      // Store announcement (would typically save to database)
      logger.info(`Announcement created: ${announcement.id}`);

      // Send announcement through specified channels
      if (announcement.scheduledAt <= new Date()) {
        await this.deliverAnnouncement(announcement);
      }

      await this.auditService.log(
        announcement.createdBy,
        'announcement_created',
        {
          announcementId: announcement.id,
          title: announcement.title,
          type: announcement.type,
          targetAudience: announcement.targetAudience,
          channels: announcement.channels
        },
        'info'
      );

      return announcement;
    } catch (error) {
      logger.error('Error creating announcement:', error);
      throw error;
    }
  }

  async deliverAnnouncement(announcement) {
    try {
      const recipients = await this.getAnnouncementRecipients(announcement.targetAudience);

      for (const channel of announcement.channels) {
        switch (channel) {
          case 'email':
            await this.sendAnnouncementEmail(recipients, announcement);
            break;
          case 'sms':
            await this.sendAnnouncementSMS(recipients, announcement);
            break;
          case 'dashboard':
            // Dashboard announcements are typically handled by the frontend
            logger.info(`Dashboard announcement ready: ${announcement.id}`);
            break;
        }
      }

      logger.info(`Announcement delivered through ${announcement.channels.length} channels`);
    } catch (error) {
      logger.error('Error delivering announcement:', error);
      throw error;
    }
  }

  async getAnnouncementRecipients(targetAudience) {
    // This would typically query the database for users based on target audience
    // For now, return a mock list
    return [
      { id: '1', email: '<EMAIL>', phone: '+1234567890' },
      { id: '2', email: '<EMAIL>', phone: '+1234567891' }
    ];
  }

  async sendAnnouncementEmail(recipients, announcement) {
    const emailPromises = recipients
      .filter(r => r.email)
      .map(recipient =>
        this.sendEmail(
          recipient.email,
          announcement.title,
          'announcement',
          {
            title: announcement.title,
            content: announcement.content,
            type: announcement.type
          }
        ).catch(error => {
          logger.error(`Failed to send announcement email to ${recipient.email}:`, error);
        })
      );

    await Promise.allSettled(emailPromises);
  }

  async sendAnnouncementSMS(recipients, announcement) {
    const smsPromises = recipients
      .filter(r => r.phone)
      .map(recipient =>
        this.sendSMS(
          recipient.phone,
          `${announcement.title}\n\n${this.stripHtml(announcement.content)}`
        ).catch(error => {
          logger.error(`Failed to send announcement SMS to ${recipient.phone}:`, error);
        })
      );

    await Promise.allSettled(smsPromises);
  }

  async configureNotifications(config) {
    try {
      this.config = { ...this.config, ...config };

      await this.auditService.log(
        null,
        'notification_config_updated',
        { config },
        'info'
      );

      logger.info('Notification service configuration updated');
      return this.config;
    } catch (error) {
      logger.error('Error configuring notifications:', error);
      throw error;
    }
  }

  async manageEmailTemplate(templateData) {
    try {
      const template = {
        id: templateData.id || this.generateId(),
        type: templateData.templateType,
        subject: templateData.subject,
        content: templateData.content,
        variables: templateData.variables || [],
        isActive: true,
        updatedAt: new Date()
      };

      // Store template (would typically save to database)
      logger.info(`Email template managed: ${template.id}`);

      await this.auditService.log(
        null,
        'email_template_updated',
        { templateId: template.id, type: template.type },
        'info'
      );

      return template;
    } catch (error) {
      logger.error('Error managing email template:', error);
      throw error;
    }
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').trim();
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}