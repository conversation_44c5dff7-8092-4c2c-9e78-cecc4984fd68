import mongoose from 'mongoose';

const projectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  collaborators: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['developer', 'designer', 'qa', 'client_reviewer', 'admin'],
      default: 'developer'
    },
    permissions: [{
      type: String,
      enum: ['read', 'write', 'deploy', 'manage_settings', 'manage_team']
    }],
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  stack: {
    type: String,
    required: true,
    enum: [
      'LAMP', 'LEMP', 'MEAN', 'MERN', 'MEVN', 'Django', 'Flask', 
      'Node.js', 'React', 'Vue.js', 'Angular', 'Laravel', 'WordPress',
      'Next.js', 'Nuxt.js', 'Gatsby', 'Svelte', 'FastAPI', 'Spring Boot',
      'Ruby on Rails', 'ASP.NET', 'Phoenix', 'Custom'
    ]
  },
  template: {
    type: String,
    enum: [
      'blank', 'wordpress', 'laravel', 'react', 'vue', 'angular', 
      'next', 'nuxt', 'gatsby', 'express', 'django', 'flask',
      'ecommerce', 'blog', 'portfolio', 'landing', 'saas', 'api'
    ]
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'archived', 'suspended'],
    default: 'active'
  },
  environments: [{
    name: {
      type: String,
      enum: ['development', 'staging', 'production'],
      required: true
    },
    url: String,
    branch: String,
    autoDeployEnabled: {
      type: Boolean,
      default: false
    },
    environmentVariables: [{
      key: String,
      value: String,
      isSecret: {
        type: Boolean,
        default: false
      }
    }],
    buildCommand: String,
    startCommand: String,
    nodeVersion: String,
    phpVersion: String,
    pythonVersion: String,
    customConfig: mongoose.Schema.Types.Mixed
  }],
  domains: [{
    domain: {
      type: String,
      required: true
    },
    subdomain: String,
    environment: {
      type: String,
      enum: ['development', 'staging', 'production'],
      default: 'production'
    },
    sslEnabled: {
      type: Boolean,
      default: false
    },
    sslProvider: {
      type: String,
      enum: ['letsencrypt', 'custom', 'cloudflare'],
      default: 'letsencrypt'
    },
    customSSL: {
      certificate: String,
      privateKey: String,
      certificateChain: String
    },
    dnsRecords: [{
      type: {
        type: String,
        enum: ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'SRV'],
        required: true
      },
      name: String,
      value: String,
      ttl: {
        type: Number,
        default: 3600
      },
      priority: Number
    }],
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  repository: {
    provider: {
      type: String,
      enum: ['github', 'gitlab', 'bitbucket', 'self-hosted', 'none']
    },
    url: String,
    branch: {
      type: String,
      default: 'main'
    },
    accessToken: String, // Encrypted
    webhookSecret: String,
    autoDeployBranches: [String],
    deployKey: String
  },
  deployment: {
    strategy: {
      type: String,
      enum: ['git', 'docker', 'zip', 'ftp'],
      default: 'git'
    },
    buildSettings: {
      buildCommand: String,
      outputDirectory: String,
      installCommand: String,
      devCommand: String
    },
    dockerConfig: {
      dockerfile: String,
      buildArgs: mongoose.Schema.Types.Mixed,
      ports: [Number],
      volumes: [String]
    },
    lastDeployment: {
      id: String,
      status: {
        type: String,
        enum: ['pending', 'building', 'deploying', 'success', 'failed', 'cancelled']
      },
      commit: String,
      branch: String,
      deployedAt: Date,
      deployedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      buildTime: Number, // in seconds
      logs: String
    }
  },
  resources: {
    allocated: {
      cpu: {
        type: Number,
        default: 1 // CPU cores
      },
      memory: {
        type: Number,
        default: 512 // MB
      },
      storage: {
        type: Number,
        default: 1024 // MB
      },
      bandwidth: {
        type: Number,
        default: 10240 // MB per month
      }
    },
    usage: {
      cpu: {
        current: Number,
        average: Number,
        peak: Number
      },
      memory: {
        current: Number,
        average: Number,
        peak: Number
      },
      storage: {
        used: Number,
        files: Number,
        databases: Number
      },
      bandwidth: {
        used: Number,
        incoming: Number,
        outgoing: Number
      }
    },
    limits: {
      maxCPU: Number,
      maxMemory: Number,
      maxStorage: Number,
      maxBandwidth: Number,
      maxDomains: Number,
      maxDatabases: Number
    }
  },
  databases: [{
    name: String,
    type: {
      type: String,
      enum: ['mysql', 'postgresql', 'mongodb', 'redis'],
      required: true
    },
    version: String,
    host: String,
    port: Number,
    username: String,
    password: String, // Encrypted
    size: Number, // MB
    backupEnabled: {
      type: Boolean,
      default: true
    },
    lastBackup: Date,
    connectionString: String,
    remoteAccessEnabled: {
      type: Boolean,
      default: false
    },
    allowedIPs: [String]
  }],
  monitoring: {
    enabled: {
      type: Boolean,
      default: true
    },
    uptimeChecks: [{
      url: String,
      interval: {
        type: Number,
        default: 300 // 5 minutes
      },
      timeout: {
        type: Number,
        default: 30 // seconds
      },
      expectedStatus: {
        type: Number,
        default: 200
      },
      isActive: {
        type: Boolean,
        default: true
      }
    }],
    alerts: [{
      type: {
        type: String,
        enum: ['uptime', 'response_time', 'cpu', 'memory', 'disk', 'error_rate']
      },
      threshold: Number,
      enabled: {
        type: Boolean,
        default: true
      },
      notificationChannels: [String]
    }],
    metrics: {
      uptime: Number, // percentage
      averageResponseTime: Number, // ms
      errorRate: Number, // percentage
      lastCheck: Date
    }
  },
  security: {
    firewall: {
      enabled: {
        type: Boolean,
        default: true
      },
      rules: [{
        name: String,
        action: {
          type: String,
          enum: ['allow', 'deny'],
          required: true
        },
        source: String, // IP or CIDR
        port: Number,
        protocol: {
          type: String,
          enum: ['tcp', 'udp', 'icmp'],
          default: 'tcp'
        }
      }]
    },
    waf: {
      enabled: {
        type: Boolean,
        default: false
      },
      rules: [String]
    },
    rateLimiting: {
      enabled: {
        type: Boolean,
        default: false
      },
      requestsPerMinute: Number,
      burstLimit: Number
    },
    bruteForceProtection: {
      enabled: {
        type: Boolean,
        default: true
      },
      maxAttempts: {
        type: Number,
        default: 5
      },
      lockoutDuration: {
        type: Number,
        default: 300 // 5 minutes
      }
    }
  },
  backups: {
    enabled: {
      type: Boolean,
      default: true
    },
    frequency: {
      type: String,
      enum: ['hourly', 'daily', 'weekly', 'monthly'],
      default: 'daily'
    },
    retention: {
      type: Number,
      default: 30 // days
    },
    includeFiles: {
      type: Boolean,
      default: true
    },
    includeDatabases: {
      type: Boolean,
      default: true
    },
    lastBackup: Date,
    backupSize: Number, // MB
    backupLocation: String
  },
  integrations: {
    analytics: [{
      provider: {
        type: String,
        enum: ['google_analytics', 'mixpanel', 'amplitude', 'custom']
      },
      trackingId: String,
      enabled: {
        type: Boolean,
        default: false
      }
    }],
    monitoring: [{
      provider: {
        type: String,
        enum: ['datadog', 'newrelic', 'sentry', 'custom']
      },
      apiKey: String,
      enabled: {
        type: Boolean,
        default: false
      }
    }],
    storage: [{
      provider: {
        type: String,
        enum: ['aws_s3', 'google_cloud', 'azure_blob', 'dropbox', 'ftp']
      },
      config: mongoose.Schema.Types.Mixed,
      enabled: {
        type: Boolean,
        default: false
      }
    }]
  },
  settings: {
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      slack: {
        type: Boolean,
        default: false
      },
      webhook: {
        type: Boolean,
        default: false
      }
    },
    customHeaders: [{
      name: String,
      value: String,
      environments: [String]
    }],
    redirects: [{
      from: String,
      to: String,
      statusCode: {
        type: Number,
        default: 301
      },
      isActive: {
        type: Boolean,
        default: true
      }
    }]
  },
  metadata: {
    tags: [String],
    category: String,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    customFields: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes
projectSchema.index({ owner: 1, status: 1 });
projectSchema.index({ slug: 1 }, { unique: true });
projectSchema.index({ 'collaborators.user': 1 });
projectSchema.index({ stack: 1, template: 1 });
projectSchema.index({ 'domains.domain': 1 });

// Virtual for project URL
projectSchema.virtual('primaryDomain').get(function() {
  const primaryDomain = this.domains.find(d => d.environment === 'production' && d.isActive);
  return primaryDomain ? primaryDomain.domain : null;
});

// Methods
projectSchema.methods.addCollaborator = function(userId, role, permissions) {
  this.collaborators.push({
    user: userId,
    role,
    permissions,
    addedAt: new Date()
  });
  return this.save();
};

projectSchema.methods.removeCollaborator = function(userId) {
  this.collaborators = this.collaborators.filter(c => !c.user.equals(userId));
  return this.save();
};

projectSchema.methods.updateResourceUsage = function(usage) {
  this.resources.usage = { ...this.resources.usage, ...usage };
  return this.save();
};

projectSchema.methods.canUserAccess = function(userId, permission = 'read') {
  if (this.owner.equals(userId)) return true;
  
  const collaborator = this.collaborators.find(c => c.user.equals(userId));
  return collaborator && collaborator.permissions.includes(permission);
};

// Static methods
projectSchema.statics.getByUser = function(userId, role = null) {
  const query = {
    $or: [
      { owner: userId },
      { 'collaborators.user': userId }
    ],
    status: { $ne: 'archived' }
  };
  
  if (role) {
    query['collaborators.role'] = role;
  }
  
  return this.find(query).populate('owner', 'username email').populate('collaborators.user', 'username email');
};

projectSchema.statics.getByStack = function(stack) {
  return this.find({ stack, status: 'active' });
};

// Pre-save middleware
projectSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  next();
});

export const Project = mongoose.model('Project', projectSchema);
