import mongoose from 'mongoose';

const resellerSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  profile: {
    businessName: {
      type: String,
      required: true
    },
    businessType: {
      type: String,
      enum: ['agency', 'freelancer', 'hosting_provider', 'consultant', 'other'],
      required: true
    },
    website: String,
    description: String,
    logo: String,
    contactInfo: {
      phone: String,
      address: {
        street: String,
        city: String,
        state: String,
        country: String,
        zipCode: String
      },
      businessHours: {
        timezone: String,
        schedule: String
      }
    },
    taxInfo: {
      taxId: String,
      vatNumber: String,
      registrationNumber: String
    }
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'active', 'suspended', 'cancelled'],
    default: 'pending'
  },
  approvalDate: Date,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  whiteLabel: {
    enabled: {
      type: Boolean,
      default: false
    },
    branding: {
      companyName: String,
      logo: String,
      favicon: String,
      primaryColor: String,
      secondaryColor: String,
      accentColor: String,
      customCSS: String,
      loginPageBg: String,
      panelTitle: String
    },
    customDomain: {
      domain: String,
      verified: {
        type: Boolean,
        default: false
      },
      sslEnabled: {
        type: Boolean,
        default: false
      },
      dnsRecords: [{
        type: String,
        name: String,
        value: String,
        verified: Boolean
      }]
    },
    emailTemplates: {
      welcome: String,
      passwordReset: String,
      billing: String,
      support: String,
      maintenance: String
    },
    supportInfo: {
      email: String,
      phone: String,
      website: String,
      knowledgeBaseUrl: String
    }
  },
  resourceLimits: {
    maxClients: {
      type: Number,
      default: 10
    },
    maxWebsites: {
      type: Number,
      default: 50
    },
    totalStorage: {
      type: Number,
      default: 100 // GB
    },
    totalBandwidth: {
      type: Number,
      default: 1000 // GB per month
    },
    maxDomains: {
      type: Number,
      default: 50
    },
    maxEmailAccounts: {
      type: Number,
      default: 100
    },
    maxDatabases: {
      type: Number,
      default: 50
    },
    maxDevelopers: {
      type: Number,
      default: 5
    }
  },
  resourceUsage: {
    clients: {
      active: {
        type: Number,
        default: 0
      },
      total: {
        type: Number,
        default: 0
      }
    },
    websites: {
      active: {
        type: Number,
        default: 0
      },
      total: {
        type: Number,
        default: 0
      }
    },
    storage: {
      used: {
        type: Number,
        default: 0
      },
      allocated: {
        type: Number,
        default: 0
      }
    },
    bandwidth: {
      used: {
        type: Number,
        default: 0
      },
      allocated: {
        type: Number,
        default: 0
      }
    },
    domains: {
      used: {
        type: Number,
        default: 0
      }
    },
    emailAccounts: {
      used: {
        type: Number,
        default: 0
      }
    },
    databases: {
      used: {
        type: Number,
        default: 0
      }
    },
    developers: {
      active: {
        type: Number,
        default: 0
      }
    }
  },
  hostingPlans: [{
    id: String,
    name: String,
    description: String,
    features: [String],
    resources: {
      storage: Number, // GB
      bandwidth: Number, // GB per month
      websites: Number,
      domains: Number,
      emailAccounts: Number,
      databases: Number,
      backupFrequency: String,
      sslIncluded: Boolean,
      cdnIncluded: Boolean,
      stagingIncluded: Boolean,
      sshAccess: Boolean
    },
    pricing: {
      basePrice: Number, // Cost from admin
      sellingPrice: Number, // Price to clients
      margin: Number, // Profit margin
      currency: String,
      billingCycle: String, // monthly, quarterly, yearly
      setupFee: Number,
      renewalPrice: Number
    },
    active: {
      type: Boolean,
      default: true
    },
    createdAt: Date,
    updatedAt: Date
  }],
  clients: [{
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Client'
    },
    plan: String,
    status: String,
    addedAt: Date,
    revenue: {
      monthly: Number,
      total: Number
    },
    customPricing: {
      enabled: Boolean,
      discountPercentage: Number,
      customPrice: Number
    }
  }],
  developers: [{
    developer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['lead', 'senior', 'junior', 'freelance'],
      default: 'junior'
    },
    permissions: [String],
    assignedClients: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Client'
    }],
    performance: {
      ticketsResolved: Number,
      averageResponseTime: Number, // hours
      clientSatisfaction: Number, // 1-5 rating
      projectsCompleted: Number
    },
    addedAt: Date,
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended'],
      default: 'active'
    }
  }],
  billing: {
    commissionRate: {
      type: Number,
      default: 20 // Percentage taken by admin
    },
    paymentMethods: [{
      type: String,
      provider: String,
      accountId: String,
      active: Boolean
    }],
    invoicing: {
      prefix: String,
      nextNumber: Number,
      taxRate: Number,
      currency: String,
      paymentTerms: Number, // days
      autoSend: Boolean
    },
    revenue: {
      thisMonth: Number,
      lastMonth: Number,
      thisYear: Number,
      lastYear: Number,
      total: Number
    },
    expenses: {
      thisMonth: Number,
      lastMonth: Number,
      thisYear: Number,
      total: Number
    }
  },
  addOnServices: [{
    id: String,
    name: String,
    description: String,
    category: String,
    pricing: {
      basePrice: Number,
      sellingPrice: Number,
      margin: Number,
      billingType: String, // one-time, monthly, yearly
      currency: String
    },
    features: [String],
    active: Boolean,
    sales: {
      total: Number,
      thisMonth: Number,
      revenue: Number
    }
  }],
  support: {
    ticketSystem: {
      enabled: Boolean,
      slaHours: Number,
      autoAssignment: Boolean,
      escalationRules: [{
        condition: String,
        action: String,
        timeLimit: Number
      }]
    },
    knowledgeBase: {
      enabled: Boolean,
      categories: [String],
      articles: Number
    },
    chatSupport: {
      enabled: Boolean,
      businessHours: String,
      autoResponder: Boolean
    },
    aiChatbot: {
      enabled: Boolean,
      provider: String,
      configuration: mongoose.Schema.Types.Mixed
    }
  },
  analytics: {
    dashboard: {
      widgets: [String],
      layout: String,
      refreshInterval: Number
    },
    reports: {
      automated: [{
        type: String,
        frequency: String,
        recipients: [String],
        enabled: Boolean
      }],
      custom: [{
        name: String,
        query: mongoose.Schema.Types.Mixed,
        schedule: String
      }]
    },
    tracking: {
      googleAnalytics: String,
      facebookPixel: String,
      customTracking: String
    }
  },
  marketplace: {
    enabled: Boolean,
    commissionRate: Number,
    categories: [String],
    featuredProducts: [String],
    customProducts: [{
      name: String,
      description: String,
      price: Number,
      category: String,
      active: Boolean
    }]
  },
  integrations: {
    crm: {
      provider: String,
      apiKey: String,
      enabled: Boolean,
      syncSettings: mongoose.Schema.Types.Mixed
    },
    accounting: {
      provider: String,
      apiKey: String,
      enabled: Boolean,
      autoSync: Boolean
    },
    marketing: {
      emailProvider: String,
      apiKey: String,
      enabled: Boolean,
      lists: [String]
    },
    support: {
      provider: String,
      apiKey: String,
      enabled: Boolean,
      ticketSync: Boolean
    },
    backup: {
      provider: String,
      config: mongoose.Schema.Types.Mixed,
      enabled: Boolean
    }
  },
  automation: {
    welcomeEmails: {
      enabled: Boolean,
      template: String,
      delay: Number // hours
    },
    billingReminders: {
      enabled: Boolean,
      schedule: [Number], // days before due
      template: String
    },
    suspensionRules: {
      enabled: Boolean,
      gracePeriod: Number, // days
      autoSuspend: Boolean
    },
    renewalReminders: {
      enabled: Boolean,
      schedule: [Number], // days before expiry
      template: String
    }
  },
  security: {
    twoFactorRequired: Boolean,
    ipWhitelist: [String],
    sessionTimeout: Number,
    passwordPolicy: {
      minLength: Number,
      requireSpecialChars: Boolean,
      requireNumbers: Boolean,
      requireUppercase: Boolean
    },
    auditLogging: {
      enabled: Boolean,
      retention: Number // days
    }
  },
  notifications: {
    preferences: {
      email: Boolean,
      sms: Boolean,
      slack: Boolean,
      webhook: String
    },
    events: {
      newClient: Boolean,
      paymentReceived: Boolean,
      paymentFailed: Boolean,
      resourceLimit: Boolean,
      supportTicket: Boolean,
      systemAlert: Boolean
    }
  },
  migration: {
    tools: {
      cpanelImport: Boolean,
      pleskImport: Boolean,
      customImport: Boolean
    },
    history: [{
      clientId: String,
      source: String,
      status: String,
      startedAt: Date,
      completedAt: Date,
      details: mongoose.Schema.Types.Mixed
    }]
  },
  api: {
    enabled: Boolean,
    keys: [{
      name: String,
      key: String,
      permissions: [String],
      lastUsed: Date,
      active: Boolean
    }],
    webhooks: [{
      url: String,
      events: [String],
      secret: String,
      active: Boolean
    }],
    rateLimit: {
      requestsPerHour: Number,
      burstLimit: Number
    }
  },
  settings: {
    timezone: String,
    language: String,
    dateFormat: String,
    currency: String,
    features: {
      whiteLabel: Boolean,
      customPlans: Boolean,
      apiAccess: Boolean,
      advancedAnalytics: Boolean,
      prioritySupport: Boolean
    }
  },
  metadata: {
    source: String,
    referralCode: String,
    tags: [String],
    notes: String,
    customFields: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes
resellerSchema.index({ user: 1 });
resellerSchema.index({ status: 1 });
resellerSchema.index({ 'whiteLabel.customDomain.domain': 1 });
resellerSchema.index({ 'clients.client': 1 });
resellerSchema.index({ 'developers.developer': 1 });

// Virtual for profit margin
resellerSchema.virtual('profitMargin').get(function() {
  if (this.billing.revenue.total === 0) return 0;
  const profit = this.billing.revenue.total - this.billing.expenses.total;
  return (profit / this.billing.revenue.total) * 100;
});

// Virtual for resource utilization
resellerSchema.virtual('resourceUtilization').get(function() {
  return {
    clients: (this.resourceUsage.clients.active / this.resourceLimits.maxClients) * 100,
    storage: (this.resourceUsage.storage.used / this.resourceLimits.totalStorage) * 100,
    bandwidth: (this.resourceUsage.bandwidth.used / this.resourceLimits.totalBandwidth) * 100
  };
});

// Methods
resellerSchema.methods.addClient = function(clientId, planId) {
  this.clients.push({
    client: clientId,
    plan: planId,
    status: 'active',
    addedAt: new Date(),
    revenue: { monthly: 0, total: 0 }
  });
  this.resourceUsage.clients.active += 1;
  this.resourceUsage.clients.total += 1;
  return this.save();
};

resellerSchema.methods.addDeveloper = function(developerId, role, permissions) {
  this.developers.push({
    developer: developerId,
    role,
    permissions,
    addedAt: new Date(),
    performance: {
      ticketsResolved: 0,
      averageResponseTime: 0,
      clientSatisfaction: 0,
      projectsCompleted: 0
    }
  });
  this.resourceUsage.developers.active += 1;
  return this.save();
};

resellerSchema.methods.createHostingPlan = function(planData) {
  const plan = {
    id: new mongoose.Types.ObjectId().toString(),
    ...planData,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  this.hostingPlans.push(plan);
  return this.save();
};

resellerSchema.methods.updateResourceUsage = function(resourceType, usage) {
  this.resourceUsage[resourceType] = { ...this.resourceUsage[resourceType], ...usage };
  return this.save();
};

resellerSchema.methods.canAddClient = function() {
  return this.resourceUsage.clients.active < this.resourceLimits.maxClients;
};

resellerSchema.methods.canAddDeveloper = function() {
  return this.resourceUsage.developers.active < this.resourceLimits.maxDevelopers;
};

// Static methods
resellerSchema.statics.getActiveResellers = function() {
  return this.find({ status: 'active' });
};

resellerSchema.statics.getByStatus = function(status) {
  return this.find({ status }).populate('user', 'email firstName lastName');
};

resellerSchema.statics.getRevenueStats = function() {
  return this.aggregate([
    { $match: { status: 'active' } },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$billing.revenue.total' },
        totalClients: { $sum: '$resourceUsage.clients.active' },
        averageRevenue: { $avg: '$billing.revenue.thisMonth' }
      }
    }
  ]);
};

export const Reseller = mongoose.model('Reseller', resellerSchema);
