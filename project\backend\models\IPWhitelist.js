import mongoose from 'mongoose';

const ipWhitelistSchema = new mongoose.Schema({
  ipAddress: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        // Validate IPv4 and IPv6 addresses, including CIDR notation
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(?:\/(?:[0-9]|[1-9][0-9]|1[0-1][0-9]|12[0-8]))?$|^::1(?:\/128)?$|^::(?:\/0)?$/;
        return ipv4Regex.test(v) || ipv6Regex.test(v);
      },
      message: 'Invalid IP address format'
    }
  },
  ipType: {
    type: String,
    enum: ['ipv4', 'ipv6', 'cidr'],
    required: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  category: {
    type: String,
    enum: ['admin', 'trusted', 'partner', 'api', 'monitoring', 'backup', 'other'],
    default: 'other'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  priority: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  allowedServices: [{
    type: String,
    enum: ['web', 'api', 'ssh', 'ftp', 'database', 'admin', 'all'],
    default: 'web'
  }],
  restrictions: {
    timeRestriction: {
      enabled: {
        type: Boolean,
        default: false
      },
      allowedHours: {
        start: {
          type: Number,
          min: 0,
          max: 23,
          default: 0
        },
        end: {
          type: Number,
          min: 0,
          max: 23,
          default: 23
        }
      },
      timezone: {
        type: String,
        default: 'UTC'
      }
    },
    rateLimiting: {
      enabled: {
        type: Boolean,
        default: false
      },
      requestsPerMinute: {
        type: Number,
        default: 60
      },
      requestsPerHour: {
        type: Number,
        default: 1000
      }
    },
    geolocation: {
      allowedCountries: [String],
      blockedCountries: [String]
    }
  },
  metadata: {
    organization: String,
    contactEmail: String,
    purpose: String,
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvalDate: Date,
    lastVerified: Date,
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'expired', 'invalid'],
      default: 'pending'
    }
  },
  usage: {
    firstSeen: Date,
    lastSeen: Date,
    totalRequests: {
      type: Number,
      default: 0
    },
    successfulRequests: {
      type: Number,
      default: 0
    },
    blockedRequests: {
      type: Number,
      default: 0
    }
  },
  alerts: {
    enabled: {
      type: Boolean,
      default: false
    },
    thresholds: {
      unusualActivity: {
        type: Number,
        default: 1000 // requests per hour
      },
      suspiciousPatterns: {
        type: Boolean,
        default: true
      }
    },
    lastAlert: Date,
    alertCount: {
      type: Number,
      default: 0
    }
  },
  expirationDate: Date,
  autoRenewal: {
    type: Boolean,
    default: false
  },
  tags: [String],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
ipWhitelistSchema.index({ ipAddress: 1 }, { unique: true });
ipWhitelistSchema.index({ isActive: 1, priority: -1 });
ipWhitelistSchema.index({ category: 1, isActive: 1 });
ipWhitelistSchema.index({ expirationDate: 1 });
ipWhitelistSchema.index({ 'metadata.verificationStatus': 1 });
ipWhitelistSchema.index({ createdBy: 1 });
ipWhitelistSchema.index({ tags: 1 });

// Virtual for checking if IP is expired
ipWhitelistSchema.virtual('isExpired').get(function() {
  return this.expirationDate && this.expirationDate < new Date();
});

// Methods
ipWhitelistSchema.methods.isAllowedAtTime = function(timestamp = new Date()) {
  if (!this.restrictions.timeRestriction.enabled) return true;
  
  const hour = timestamp.getHours();
  const { start, end } = this.restrictions.timeRestriction.allowedHours;
  
  if (start <= end) {
    return hour >= start && hour <= end;
  } else {
    // Handles overnight ranges (e.g., 22:00 to 06:00)
    return hour >= start || hour <= end;
  }
};

ipWhitelistSchema.methods.checkRateLimit = function(requestCount, timeWindow = 'minute') {
  if (!this.restrictions.rateLimiting.enabled) return true;
  
  const limits = {
    minute: this.restrictions.rateLimiting.requestsPerMinute,
    hour: this.restrictions.rateLimiting.requestsPerHour
  };
  
  return requestCount <= (limits[timeWindow] || Infinity);
};

ipWhitelistSchema.methods.updateUsage = function(successful = true) {
  this.usage.lastSeen = new Date();
  if (!this.usage.firstSeen) {
    this.usage.firstSeen = new Date();
  }
  
  this.usage.totalRequests += 1;
  if (successful) {
    this.usage.successfulRequests += 1;
  } else {
    this.usage.blockedRequests += 1;
  }
  
  return this.save();
};

ipWhitelistSchema.methods.shouldAlert = function() {
  if (!this.alerts.enabled) return false;
  
  const hourlyRequests = this.usage.totalRequests; // Simplified - would need time-based calculation
  return hourlyRequests > this.alerts.thresholds.unusualActivity;
};

// Static methods
ipWhitelistSchema.statics.findByIP = function(ipAddress) {
  return this.findOne({ 
    ipAddress, 
    isActive: true,
    $or: [
      { expirationDate: { $exists: false } },
      { expirationDate: { $gt: new Date() } }
    ]
  });
};

ipWhitelistSchema.statics.getActiveIPs = function(category = null) {
  const query = { 
    isActive: true,
    $or: [
      { expirationDate: { $exists: false } },
      { expirationDate: { $gt: new Date() } }
    ]
  };
  
  if (category) {
    query.category = category;
  }
  
  return this.find(query).sort({ priority: -1, createdAt: -1 });
};

ipWhitelistSchema.statics.getExpiredIPs = function() {
  return this.find({
    expirationDate: { $lt: new Date() },
    isActive: true
  });
};

ipWhitelistSchema.statics.cleanupExpired = function() {
  return this.updateMany(
    { 
      expirationDate: { $lt: new Date() },
      autoRenewal: false
    },
    { isActive: false }
  );
};

// Pre-save middleware
ipWhitelistSchema.pre('save', function(next) {
  // Determine IP type
  if (this.ipAddress.includes(':')) {
    this.ipType = 'ipv6';
  } else if (this.ipAddress.includes('/')) {
    this.ipType = 'cidr';
  } else {
    this.ipType = 'ipv4';
  }
  
  // Set default allowed services if not specified
  if (!this.allowedServices || this.allowedServices.length === 0) {
    this.allowedServices = ['web'];
  }
  
  next();
});

// Pre-remove middleware
ipWhitelistSchema.pre('remove', function(next) {
  // Log the removal for audit purposes
  console.log(`IP whitelist entry removed: ${this.ipAddress}`);
  next();
});

export const IPWhitelist = mongoose.model('IPWhitelist', ipWhitelistSchema);
