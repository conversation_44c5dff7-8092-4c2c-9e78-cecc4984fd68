import rateLimit from 'express-rate-limit';
import { logger } from '../utils/logger.js';
import { SecurityPolicy } from '../models/SecurityPolicy.js';
import { SecurityAuditLog } from '../models/SecurityAuditLog.js';
import { IPWhitelist } from '../models/IPWhitelist.js';
import { FirewallRule } from '../models/FirewallRule.js';
import { SecurityScan } from '../models/SecurityScan.js';
import { CacheService } from './cacheService.js';

export class SecurityService {
  constructor(config = {}) {
    this.config = {
      maxLoginAttempts: 5,
      lockoutDuration: 30 * 60 * 1000, // 30 minutes
      ...config
    };
    this.cacheService = new CacheService(config.cache);
    this.defaultPolicies = {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90,
        preventReuse: 5
      },
      sessionPolicy: {
        maxDuration: 24 * 60 * 60 * 1000,
        idleTimeout: 2 * 60 * 60 * 1000,
        maxConcurrentSessions: 3
      }
    };
  }

  async updatePolicy(policyData) {
    try {
      const policy = await SecurityPolicy.findOneAndUpdate(
        { type: 'global' },
        {
          ...policyData,
          updatedAt: new Date()
        },
        {
          upsert: true,
          new: true
        }
      );

      await this.logSecurityEvent('policy_updated', {
        policyId: policy._id,
        changes: policyData
      });

      logger.info('Security policy updated successfully');
      return policy;
    } catch (error) {
      logger.error('Error updating security policy:', error);
      throw error;
    }
  }

  async getAuditLogs(filters = {}) {
    try {
      const {
        startDate,
        endDate,
        severity,
        eventType,
        userId,
        limit = 100,
        page = 1
      } = filters;

      const query = {};

      if (startDate && endDate) {
        query.timestamp = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      if (severity) query.severity = severity;
      if (eventType) query.eventType = eventType;
      if (userId) query.userId = userId;

      const logs = await SecurityAuditLog.find(query)
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip((page - 1) * limit)
        .populate('userId', 'username email');

      const total = await SecurityAuditLog.countDocuments(query);

      return {
        logs,
        pagination: {
          total,
          page,
          pages: Math.ceil(total / limit),
          limit
        }
      };
    } catch (error) {
      logger.error('Error fetching audit logs:', error);
      throw error;
    }
  }

  async manageIPWhitelist(action, ips) {
    try {
      let result;

      switch (action) {
        case 'add':
          result = await IPWhitelist.insertMany(
            ips.map(ip => ({
              ipAddress: ip.address,
              description: ip.description,
              addedAt: new Date(),
              isActive: true
            }))
          );
          break;

        case 'remove':
          result = await IPWhitelist.deleteMany({
            ipAddress: { $in: ips }
          });
          break;

        case 'toggle':
          result = await IPWhitelist.updateMany(
            { ipAddress: { $in: ips.map(ip => ip.address) } },
            { isActive: ips[0].isActive }
          );
          break;

        default:
          throw new Error('Invalid action for IP whitelist management');
      }

      await this.logSecurityEvent('ip_whitelist_modified', {
        action,
        ips,
        result
      });

      return result;
    } catch (error) {
      logger.error('Error managing IP whitelist:', error);
      throw error;
    }
  }

  async configureFirewallRules(rules) {
    try {
      if (rules.clearExisting) {
        await FirewallRule.deleteMany({});
      }

      const newRules = await FirewallRule.insertMany(
        rules.rules.map(rule => ({
          ...rule,
          createdAt: new Date(),
          isActive: true
        }))
      );

      await this.logSecurityEvent('firewall_rules_configured', {
        rulesCount: newRules.length,
        clearExisting: rules.clearExisting
      });

      logger.info(`Configured ${newRules.length} firewall rules`);
      return newRules;
    } catch (error) {
      logger.error('Error configuring firewall rules:', error);
      throw error;
    }
  }

  async initiateScan(scanType, targets) {
    try {
      const scan = new SecurityScan({
        scanType,
        targets,
        status: 'initiated',
        startedAt: new Date(),
        progress: 0
      });

      await scan.save();
      this.performScan(scan._id, scanType, targets);

      await this.logSecurityEvent('security_scan_initiated', {
        scanId: scan._id,
        scanType,
        targetsCount: targets.length
      });

      return scan._id;
    } catch (error) {
      logger.error('Error initiating security scan:', error);
      throw error;
    }
  }

  async performScan(scanId, scanType, targets) {
    try {
      await SecurityScan.findByIdAndUpdate(scanId, {
        status: 'running',
        progress: 10
      });

      const results = await this.executeScanLogic(scanType, targets);

      await SecurityScan.findByIdAndUpdate(scanId, {
        status: 'completed',
        progress: 100,
        completedAt: new Date(),
        results
      });

      await this.logSecurityEvent('security_scan_completed', {
        scanId,
        vulnerabilitiesFound: results.vulnerabilities?.length || 0
      });

    } catch (error) {
      await SecurityScan.findByIdAndUpdate(scanId, {
        status: 'failed',
        error: error.message,
        completedAt: new Date()
      });

      logger.error('Error performing security scan:', error);
    }
  }

  async executeScanLogic(scanType, targets) {
    const results = {
      vulnerabilities: [],
      summary: { high: 0, medium: 0, low: 0, info: 0 }
    };

    switch (scanType) {
      case 'vulnerability':
        results.vulnerabilities = this.simulateVulnerabilityResults(targets);
        break;
      case 'port':
        results.openPorts = this.simulatePortScanResults(targets);
        break;
      case 'malware':
        results.threats = this.simulateMalwareScanResults(targets);
        break;
    }

    return results;
  }

  simulateVulnerabilityResults(targets) {
    return [
      {
        severity: 'medium',
        title: 'Outdated SSL/TLS Configuration',
        description: 'Server supports deprecated TLS versions',
        target: targets[0],
        cvss: 5.3
      }
    ];
  }

  simulatePortScanResults(targets) {
    return targets.map(target => ({
      target,
      openPorts: [22, 80, 443, 3306],
      closedPorts: [21, 23, 25]
    }));
  }

  simulateMalwareScanResults(targets) {
    return {
      threatsFound: 0,
      filesScanned: 1000,
      cleanFiles: 1000
    };
  }

  async logSecurityEvent(eventType, data, severity = 'info') {
    try {
      const logEntry = new SecurityAuditLog({
        eventType,
        severity,
        data,
        timestamp: new Date(),
        source: 'SecurityService'
      });

      await logEntry.save();
    } catch (error) {
      logger.error('Error logging security event:', error);
    }
  }

  getDDoSProtection() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later',
      handler: async (req, res) => {
        await this.loggingService.logSystemEvent('DDoS_ATTEMPT', 'WARNING', {
          ip: req.ip,
          path: req.path,
          headers: req.headers
        });
        res.status(429).json({
          error: 'Too many requests from this IP, please try again later'
        });
      }
    });
  }

  async scanForMalware(file) {
    try {
      const result = await this.clamav.scanFile(file.path);
      await this.loggingService.logSystemEvent('MALWARE_SCAN', 'INFO', {
        filename: file.originalname,
        result: result
      });
      return result;
    } catch (error) {
      await this.loggingService.logSystemEvent('MALWARE_SCAN_ERROR', 'ERROR', {
        filename: file.originalname,
        error: error.message
      });
      throw new Error('Malware scan failed');
    }
  }

  async trackLoginAttempt(userId, ip, success) {
    const key = `login_attempts:${ip}`;
    let attempts = await this.cacheService.get(key) || 0;

    if (!success) {
      attempts++;
      await this.cacheService.set(key, attempts, 3600); // 1 hour

      if (attempts >= this.config.maxLoginAttempts) {
        await this.blockIP(ip);
        await this.logSecurityEvent('BRUTE_FORCE_DETECTED', {
          ip,
          userId,
          attempts
        }, 'warning');
        throw new Error('Account locked due to too many failed attempts');
      }
    } else {
      await this.cacheService.delete(key);
    }

    await this.logSecurityEvent(
      success ? 'login_success' : 'login_failure',
      { userId, ip, attempts },
      success ? 'info' : 'warning'
    );
  }

  async blockIP(ip) {
    const key = `blocked_ip:${ip}`;
    await this.cacheService.set(key, true, 24 * 3600); // 24 hours
    await this.logSecurityEvent('IP_BLOCKED', { ip }, 'warning');
  }

  async isIPBlocked(ip) {
    const key = `blocked_ip:${ip}`;
    return await this.cacheService.get(key) || false;
  }

  async getSecurityMetrics(timeRange = '7d') {
    try {
      const startDate = new Date();

      switch (timeRange) {
        case '1h':
          startDate.setHours(startDate.getHours() - 1);
          break;
        case '24h':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
      }

      const metrics = await SecurityAuditLog.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$eventType',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        timeRange,
        ddosAttempts: 0,
        bruteForceAttempts: 0,
        malwareDetections: 0,
        totalScans: 0,
        loginFailures: 0,
        securityScans: 0
      };

      metrics.forEach(metric => {
        switch (metric._id) {
          case 'DDoS_ATTEMPT':
            result.ddosAttempts = metric.count;
            break;
          case 'BRUTE_FORCE_DETECTED':
            result.bruteForceAttempts = metric.count;
            break;
          case 'MALWARE_SCAN':
            result.totalScans = metric.count;
            break;
          case 'login_failure':
            result.loginFailures = metric.count;
            break;
          case 'security_scan_completed':
            result.securityScans = metric.count;
            break;
        }
      });

      return result;
    } catch (error) {
      logger.error('Failed to get security metrics:', error);
      throw new Error('Failed to get security metrics');
    }
  }

  async updateGlobalPolicy(policy) {
    return await this.updatePolicy(policy);
  }

  async getComplianceStatus() {
    try {
      const policies = await SecurityPolicy.find({ type: 'global' });
      const recentScans = await SecurityScan.find({
        completedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }).sort({ completedAt: -1 }).limit(10);

      return {
        policiesConfigured: policies.length > 0,
        lastPolicyUpdate: policies[0]?.updatedAt,
        recentScansCount: recentScans.length,
        lastScanDate: recentScans[0]?.completedAt,
        complianceScore: this.calculateComplianceScore(policies, recentScans)
      };
    } catch (error) {
      logger.error('Error getting compliance status:', error);
      throw error;
    }
  }

  calculateComplianceScore(policies, scans) {
    let score = 0;

    // Base score for having policies
    if (policies.length > 0) score += 30;

    // Score for recent policy updates (within 90 days)
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    if (policies.some(p => p.updatedAt > ninetyDaysAgo)) score += 20;

    // Score for recent security scans
    if (scans.length > 0) score += 25;

    // Score for regular scanning (more than 1 scan in 30 days)
    if (scans.length > 1) score += 25;

    return Math.min(score, 100);
  }
}