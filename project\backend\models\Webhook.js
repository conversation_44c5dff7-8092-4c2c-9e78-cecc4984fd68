import mongoose from 'mongoose';
import crypto from 'crypto';

const webhookSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  url: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'URL must be a valid HTTP or HTTPS URL'
    }
  },
  method: {
    type: String,
    enum: ['POST', 'PUT', 'PATCH'],
    default: 'POST'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'disabled', 'failed'],
    default: 'active'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  events: [{
    type: String,
    enum: [
      // User events
      'user.created', 'user.updated', 'user.deleted', 'user.login', 'user.logout',
      'user.password_changed', 'user.email_verified', 'user.suspended', 'user.activated',
      
      // Billing events
      'billing.payment_succeeded', 'billing.payment_failed', 'billing.subscription_created',
      'billing.subscription_updated', 'billing.subscription_cancelled', 'billing.invoice_created',
      'billing.invoice_paid', 'billing.refund_created',
      
      // Server events
      'server.created', 'server.updated', 'server.deleted', 'server.started', 'server.stopped',
      'server.backup_created', 'server.backup_restored', 'server.maintenance_started',
      'server.maintenance_completed',
      
      // Domain events
      'domain.created', 'domain.updated', 'domain.deleted', 'domain.dns_updated',
      'domain.ssl_installed', 'domain.ssl_renewed',
      
      // Database events
      'database.created', 'database.updated', 'database.deleted', 'database.backup_created',
      'database.backup_restored',
      
      // Support events
      'ticket.created', 'ticket.updated', 'ticket.resolved', 'ticket.closed',
      'ticket.escalated', 'ticket.assigned',
      
      // Security events
      'security.login_failed', 'security.suspicious_activity', 'security.ip_blocked',
      'security.scan_completed', 'security.vulnerability_detected',
      
      // System events
      'system.backup_completed', 'system.maintenance_started', 'system.maintenance_completed',
      'system.alert_triggered', 'system.service_down', 'system.service_restored',
      
      // API events
      'api.key_created', 'api.key_revoked', 'api.rate_limit_exceeded',
      
      // Custom events
      'custom.*'
    ]
  }],
  headers: {
    type: Map,
    of: String,
    default: new Map()
  },
  authentication: {
    type: {
      type: String,
      enum: ['none', 'basic', 'bearer', 'api_key', 'oauth2'],
      default: 'none'
    },
    credentials: {
      username: String,
      password: String, // Should be encrypted
      token: String,    // Should be encrypted
      apiKey: String,   // Should be encrypted
      apiKeyHeader: String,
      oauth2: {
        clientId: String,
        clientSecret: String, // Should be encrypted
        tokenUrl: String,
        scope: String
      }
    }
  },
  security: {
    secret: {
      type: String,
      default: function() {
        return crypto.randomBytes(32).toString('hex');
      }
    },
    signatureHeader: {
      type: String,
      default: 'X-Webhook-Signature'
    },
    algorithm: {
      type: String,
      enum: ['sha256', 'sha1', 'md5'],
      default: 'sha256'
    },
    verifySSL: {
      type: Boolean,
      default: true
    },
    allowedIPs: [String],
    timeout: {
      type: Number,
      default: 30000, // 30 seconds
      min: 1000,
      max: 300000
    }
  },
  delivery: {
    retryPolicy: {
      enabled: {
        type: Boolean,
        default: true
      },
      maxAttempts: {
        type: Number,
        default: 3,
        min: 1,
        max: 10
      },
      backoffStrategy: {
        type: String,
        enum: ['linear', 'exponential', 'fixed'],
        default: 'exponential'
      },
      initialDelay: {
        type: Number,
        default: 1000 // 1 second
      },
      maxDelay: {
        type: Number,
        default: 300000 // 5 minutes
      }
    },
    batchDelivery: {
      enabled: {
        type: Boolean,
        default: false
      },
      batchSize: {
        type: Number,
        default: 10,
        min: 1,
        max: 100
      },
      batchTimeout: {
        type: Number,
        default: 60000 // 1 minute
      }
    }
  },
  filtering: {
    conditions: [{
      field: String,
      operator: {
        type: String,
        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'starts_with', 'ends_with', 'regex']
      },
      value: String
    }],
    enabled: {
      type: Boolean,
      default: false
    }
  },
  statistics: {
    totalDeliveries: {
      type: Number,
      default: 0
    },
    successfulDeliveries: {
      type: Number,
      default: 0
    },
    failedDeliveries: {
      type: Number,
      default: 0
    },
    lastDelivery: {
      timestamp: Date,
      status: String,
      responseCode: Number,
      responseTime: Number,
      error: String
    },
    averageResponseTime: {
      type: Number,
      default: 0
    },
    uptime: {
      type: Number,
      default: 100 // percentage
    },
    lastSuccessfulDelivery: Date,
    lastFailedDelivery: Date
  },
  deliveryHistory: [{
    eventType: String,
    eventId: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    attempt: Number,
    status: {
      type: String,
      enum: ['pending', 'delivered', 'failed', 'retrying']
    },
    responseCode: Number,
    responseTime: Number,
    responseHeaders: Map,
    responseBody: String,
    error: String,
    payload: mongoose.Schema.Types.Mixed
  }],
  testing: {
    lastTest: Date,
    testResults: [{
      timestamp: {
        type: Date,
        default: Date.now
      },
      success: Boolean,
      responseCode: Number,
      responseTime: Number,
      error: String,
      testPayload: mongoose.Schema.Types.Mixed
    }]
  },
  metadata: {
    tags: [String],
    environment: {
      type: String,
      enum: ['production', 'staging', 'development', 'testing'],
      default: 'production'
    },
    application: String,
    version: String,
    customFields: mongoose.Schema.Types.Mixed
  },
  audit: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    disabledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    disabledAt: Date,
    disabledReason: String
  }
}, {
  timestamps: true
});

// Indexes
webhookSchema.index({ userId: 1, status: 1 });
webhookSchema.index({ events: 1, status: 1 });
webhookSchema.index({ status: 1, 'statistics.lastDelivery.timestamp': -1 });
webhookSchema.index({ 'metadata.environment': 1, status: 1 });
webhookSchema.index({ 'metadata.tags': 1 });

// Virtual for success rate
webhookSchema.virtual('successRate').get(function() {
  if (this.statistics.totalDeliveries === 0) return 0;
  return (this.statistics.successfulDeliveries / this.statistics.totalDeliveries) * 100;
});

// Virtual for checking if webhook is healthy
webhookSchema.virtual('isHealthy').get(function() {
  return this.status === 'active' && this.successRate >= 95;
});

// Methods
webhookSchema.methods.generateSignature = function(payload) {
  const payloadString = typeof payload === 'string' ? payload : JSON.stringify(payload);
  return crypto
    .createHmac(this.security.algorithm, this.security.secret)
    .update(payloadString)
    .digest('hex');
};

webhookSchema.methods.verifySignature = function(payload, signature) {
  const expectedSignature = this.generateSignature(payload);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
};

webhookSchema.methods.shouldTrigger = function(eventType, eventData) {
  // Check if webhook is active
  if (this.status !== 'active') return false;
  
  // Check if event type is subscribed
  const isSubscribed = this.events.some(event => {
    if (event === eventType) return true;
    if (event.endsWith('*')) {
      const prefix = event.slice(0, -1);
      return eventType.startsWith(prefix);
    }
    return false;
  });
  
  if (!isSubscribed) return false;
  
  // Check filtering conditions
  if (this.filtering.enabled && this.filtering.conditions.length > 0) {
    return this.matchesFilters(eventData);
  }
  
  return true;
};

webhookSchema.methods.matchesFilters = function(eventData) {
  return this.filtering.conditions.every(condition => {
    const fieldValue = this.getNestedValue(eventData, condition.field);
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'not_equals':
        return fieldValue !== condition.value;
      case 'contains':
        return String(fieldValue).includes(condition.value);
      case 'not_contains':
        return !String(fieldValue).includes(condition.value);
      case 'starts_with':
        return String(fieldValue).startsWith(condition.value);
      case 'ends_with':
        return String(fieldValue).endsWith(condition.value);
      case 'regex':
        return new RegExp(condition.value).test(String(fieldValue));
      default:
        return true;
    }
  });
};

webhookSchema.methods.getNestedValue = function(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
};

webhookSchema.methods.recordDelivery = function(eventType, eventId, attempt, status, responseCode, responseTime, error = null, payload = null) {
  // Update statistics
  this.statistics.totalDeliveries += 1;
  
  if (status === 'delivered') {
    this.statistics.successfulDeliveries += 1;
    this.statistics.lastSuccessfulDelivery = new Date();
  } else if (status === 'failed') {
    this.statistics.failedDeliveries += 1;
    this.statistics.lastFailedDelivery = new Date();
  }
  
  // Update average response time
  if (responseTime > 0) {
    const totalResponseTime = this.statistics.averageResponseTime * (this.statistics.totalDeliveries - 1);
    this.statistics.averageResponseTime = (totalResponseTime + responseTime) / this.statistics.totalDeliveries;
  }
  
  // Update last delivery
  this.statistics.lastDelivery = {
    timestamp: new Date(),
    status,
    responseCode,
    responseTime,
    error
  };
  
  // Update uptime
  this.statistics.uptime = this.successRate;
  
  // Add to delivery history (keep last 100 entries)
  this.deliveryHistory.unshift({
    eventType,
    eventId,
    timestamp: new Date(),
    attempt,
    status,
    responseCode,
    responseTime,
    error,
    payload
  });
  
  if (this.deliveryHistory.length > 100) {
    this.deliveryHistory = this.deliveryHistory.slice(0, 100);
  }
  
  // Update webhook status based on recent failures
  const recentFailures = this.deliveryHistory
    .slice(0, 10)
    .filter(delivery => delivery.status === 'failed').length;
  
  if (recentFailures >= 8) {
    this.status = 'failed';
  } else if (this.status === 'failed' && recentFailures < 3) {
    this.status = 'active';
  }
  
  return this.save();
};

webhookSchema.methods.test = function(testPayload = null) {
  const payload = testPayload || {
    event: 'webhook.test',
    timestamp: new Date().toISOString(),
    data: { message: 'This is a test webhook delivery' }
  };
  
  // This would trigger an actual HTTP request in a real implementation
  // For now, we'll just record a test result
  this.testing.lastTest = new Date();
  this.testing.testResults.unshift({
    timestamp: new Date(),
    success: true,
    responseCode: 200,
    responseTime: 150,
    testPayload: payload
  });
  
  // Keep only last 10 test results
  if (this.testing.testResults.length > 10) {
    this.testing.testResults = this.testing.testResults.slice(0, 10);
  }
  
  return this.save();
};

webhookSchema.methods.disable = function(disabledBy, reason) {
  this.status = 'disabled';
  this.audit.disabledBy = disabledBy;
  this.audit.disabledAt = new Date();
  this.audit.disabledReason = reason;
  return this.save();
};

// Static methods
webhookSchema.statics.getActiveWebhooks = function(eventType = null) {
  const query = { status: 'active' };
  if (eventType) {
    query.$or = [
      { events: eventType },
      { events: { $regex: '^' + eventType.split('.')[0] + '\\.\\*$' } }
    ];
  }
  return this.find(query).populate('userId', 'username email');
};

webhookSchema.statics.getWebhooksByUser = function(userId) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

webhookSchema.statics.getFailedWebhooks = function() {
  return this.find({ 
    status: { $in: ['failed', 'disabled'] },
    'statistics.lastFailedDelivery': { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
  }).populate('userId', 'username email');
};

webhookSchema.statics.getWebhookStatistics = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalDeliveries: { $sum: '$statistics.totalDeliveries' },
        successfulDeliveries: { $sum: '$statistics.successfulDeliveries' },
        avgResponseTime: { $avg: '$statistics.averageResponseTime' }
      }
    }
  ]);
};

// Pre-save middleware
webhookSchema.pre('save', function(next) {
  // Ensure headers is a Map
  if (!(this.headers instanceof Map)) {
    this.headers = new Map(Object.entries(this.headers || {}));
  }
  
  // Set default headers
  if (!this.headers.has('Content-Type')) {
    this.headers.set('Content-Type', 'application/json');
  }
  if (!this.headers.has('User-Agent')) {
    this.headers.set('User-Agent', 'NextGenPanel-Webhook/1.0');
  }
  
  next();
});

export const Webhook = mongoose.model('Webhook', webhookSchema);
