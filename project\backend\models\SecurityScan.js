import mongoose from 'mongoose';

const vulnerabilitySchema = new mongoose.Schema({
  id: String,
  title: {
    type: String,
    required: true
  },
  description: String,
  severity: {
    type: String,
    enum: ['critical', 'high', 'medium', 'low', 'info'],
    required: true
  },
  cvss: {
    score: {
      type: Number,
      min: 0,
      max: 10
    },
    vector: String,
    version: {
      type: String,
      enum: ['2.0', '3.0', '3.1'],
      default: '3.1'
    }
  },
  cve: [String], // CVE identifiers
  cwe: [String], // CWE identifiers
  target: {
    type: String,
    required: true
  },
  port: Number,
  service: String,
  protocol: String,
  evidence: String,
  solution: String,
  references: [String],
  exploitable: {
    type: Boolean,
    default: false
  },
  verified: {
    type: Boolean,
    default: false
  },
  falsePositive: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['open', 'fixed', 'mitigated', 'accepted', 'false_positive'],
    default: 'open'
  },
  firstDetected: {
    type: Date,
    default: Date.now
  },
  lastSeen: Date,
  fixedDate: Date
});

const securityScanSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  description: String,
  scanType: {
    type: String,
    required: true,
    enum: [
      'vulnerability',
      'port',
      'malware',
      'compliance',
      'penetration',
      'web_application',
      'network',
      'infrastructure',
      'database',
      'configuration',
      'ssl_tls',
      'dns',
      'email_security'
    ]
  },
  status: {
    type: String,
    required: true,
    enum: ['queued', 'initiated', 'running', 'paused', 'completed', 'failed', 'cancelled', 'timeout'],
    default: 'queued'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'critical'],
    default: 'normal'
  },
  targets: [{
    type: {
      type: String,
      enum: ['ip', 'domain', 'url', 'network', 'service'],
      required: true
    },
    value: {
      type: String,
      required: true
    },
    ports: [Number],
    credentials: {
      username: String,
      password: String, // Should be encrypted
      keyFile: String
    }
  }],
  configuration: {
    scanProfile: {
      type: String,
      enum: ['quick', 'standard', 'comprehensive', 'custom'],
      default: 'standard'
    },
    intensity: {
      type: Number,
      min: 1,
      max: 10,
      default: 5
    },
    timeout: {
      type: Number,
      default: 3600 // seconds
    },
    maxConcurrentTargets: {
      type: Number,
      default: 10
    },
    excludePatterns: [String],
    includePatterns: [String],
    customOptions: mongoose.Schema.Types.Mixed
  },
  schedule: {
    type: {
      type: String,
      enum: ['immediate', 'scheduled', 'recurring'],
      default: 'immediate'
    },
    scheduledTime: Date,
    recurring: {
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'quarterly']
      },
      interval: Number,
      daysOfWeek: [Number], // 0-6, Sunday = 0
      dayOfMonth: Number,
      endDate: Date
    }
  },
  execution: {
    startedAt: Date,
    completedAt: Date,
    duration: Number, // seconds
    progress: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    currentTarget: String,
    processedTargets: {
      type: Number,
      default: 0
    },
    totalTargets: {
      type: Number,
      default: 0
    },
    engine: {
      name: String,
      version: String,
      signature_version: String
    },
    worker: {
      id: String,
      hostname: String,
      ip: String
    }
  },
  results: {
    summary: {
      vulnerabilities: {
        critical: { type: Number, default: 0 },
        high: { type: Number, default: 0 },
        medium: { type: Number, default: 0 },
        low: { type: Number, default: 0 },
        info: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
      },
      ports: {
        open: { type: Number, default: 0 },
        closed: { type: Number, default: 0 },
        filtered: { type: Number, default: 0 }
      },
      services: {
        identified: { type: Number, default: 0 },
        vulnerable: { type: Number, default: 0 }
      },
      compliance: {
        passed: { type: Number, default: 0 },
        failed: { type: Number, default: 0 },
        score: { type: Number, min: 0, max: 100 }
      }
    },
    vulnerabilities: [vulnerabilitySchema],
    openPorts: [{
      target: String,
      port: Number,
      protocol: String,
      service: String,
      version: String,
      state: String
    }],
    services: [{
      target: String,
      port: Number,
      name: String,
      version: String,
      banner: String,
      vulnerabilities: [String] // References to vulnerability IDs
    }],
    certificates: [{
      target: String,
      port: Number,
      issuer: String,
      subject: String,
      validFrom: Date,
      validTo: Date,
      algorithm: String,
      keySize: Number,
      issues: [String]
    }],
    compliance: [{
      standard: String,
      requirement: String,
      status: {
        type: String,
        enum: ['pass', 'fail', 'warning', 'not_applicable']
      },
      description: String,
      remediation: String
    }],
    rawOutput: String,
    artifacts: [{
      name: String,
      type: String,
      path: String,
      size: Number
    }]
  },
  notifications: {
    onComplete: {
      enabled: { type: Boolean, default: true },
      recipients: [String],
      webhook: String
    },
    onCritical: {
      enabled: { type: Boolean, default: true },
      recipients: [String],
      webhook: String
    },
    onFailure: {
      enabled: { type: Boolean, default: true },
      recipients: [String]
    }
  },
  reporting: {
    formats: [{
      type: String,
      enum: ['pdf', 'html', 'xml', 'json', 'csv']
    }],
    template: String,
    customFields: mongoose.Schema.Types.Mixed,
    distribution: [{
      email: String,
      format: String
    }]
  },
  metadata: {
    tags: [String],
    category: String,
    environment: {
      type: String,
      enum: ['production', 'staging', 'development', 'testing']
    },
    compliance_frameworks: [String],
    business_unit: String,
    cost_center: String
  },
  error: {
    code: String,
    message: String,
    details: mongoose.Schema.Types.Mixed,
    timestamp: Date
  },
  parentScan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SecurityScan'
  },
  childScans: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SecurityScan'
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
securityScanSchema.index({ status: 1, createdAt: -1 });
securityScanSchema.index({ scanType: 1, status: 1 });
securityScanSchema.index({ createdBy: 1, createdAt: -1 });
securityScanSchema.index({ 'schedule.scheduledTime': 1 });
securityScanSchema.index({ 'execution.startedAt': -1 });
securityScanSchema.index({ 'metadata.tags': 1 });
securityScanSchema.index({ 'metadata.environment': 1 });
securityScanSchema.index({ priority: 1, status: 1 });

// Virtual for scan duration in human readable format
securityScanSchema.virtual('durationFormatted').get(function() {
  if (!this.execution.duration) return null;
  
  const hours = Math.floor(this.execution.duration / 3600);
  const minutes = Math.floor((this.execution.duration % 3600) / 60);
  const seconds = this.execution.duration % 60;
  
  return `${hours}h ${minutes}m ${seconds}s`;
});

// Methods
securityScanSchema.methods.updateProgress = function(progress, currentTarget = null) {
  this.execution.progress = Math.min(Math.max(progress, 0), 100);
  if (currentTarget) {
    this.execution.currentTarget = currentTarget;
  }
  return this.save();
};

securityScanSchema.methods.addVulnerability = function(vulnerability) {
  this.results.vulnerabilities.push(vulnerability);
  this.results.summary.vulnerabilities[vulnerability.severity] += 1;
  this.results.summary.vulnerabilities.total += 1;
  return this.save();
};

securityScanSchema.methods.calculateRiskScore = function() {
  const weights = { critical: 10, high: 7, medium: 4, low: 2, info: 1 };
  const summary = this.results.summary.vulnerabilities;
  
  return (
    summary.critical * weights.critical +
    summary.high * weights.high +
    summary.medium * weights.medium +
    summary.low * weights.low +
    summary.info * weights.info
  );
};

securityScanSchema.methods.markComplete = function() {
  this.status = 'completed';
  this.execution.completedAt = new Date();
  this.execution.progress = 100;
  
  if (this.execution.startedAt) {
    this.execution.duration = Math.floor(
      (this.execution.completedAt - this.execution.startedAt) / 1000
    );
  }
  
  return this.save();
};

securityScanSchema.methods.markFailed = function(error) {
  this.status = 'failed';
  this.execution.completedAt = new Date();
  this.error = {
    code: error.code || 'SCAN_FAILED',
    message: error.message,
    details: error.details,
    timestamp: new Date()
  };
  
  return this.save();
};

// Static methods
securityScanSchema.statics.getActiveScans = function() {
  return this.find({
    status: { $in: ['queued', 'initiated', 'running', 'paused'] }
  }).sort({ priority: -1, createdAt: 1 });
};

securityScanSchema.statics.getScheduledScans = function() {
  return this.find({
    'schedule.type': { $in: ['scheduled', 'recurring'] },
    'schedule.scheduledTime': { $lte: new Date() },
    status: 'queued'
  });
};

securityScanSchema.statics.getScanStatistics = function(timeRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);
  
  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgDuration: { $avg: '$execution.duration' }
      }
    }
  ]);
};

// Pre-save middleware
securityScanSchema.pre('save', function(next) {
  if (this.isNew) {
    this.execution.totalTargets = this.targets.length;
  }
  
  if (this.status === 'running' && !this.execution.startedAt) {
    this.execution.startedAt = new Date();
  }
  
  next();
});

export const SecurityScan = mongoose.model('SecurityScan', securityScanSchema);
