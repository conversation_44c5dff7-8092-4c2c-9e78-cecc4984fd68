import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { logger } from '../utils/logger.js';
import { AuditLog } from '../models/AuditLog.js';
import { LoginHistory } from '../models/LoginHistory.js';

const writeFile = promisify(fs.writeFile);
const appendFile = promisify(fs.appendFile);

export class AuditLogService {
  constructor(config = {}) {
    this.config = config;
    this.logDir = path.join(process.cwd(), 'logs', 'audit');
    this.retentionPeriod = 365; // days
    this.ensureLogDirectory();
  }

  async ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  async log(userId, action, details = {}, severity = 'info') {
    try {
      const logEntry = new AuditLog({
        userId,
        action,
        details,
        severity,
        timestamp: new Date(),
        ipAddress: details.ipAddress,
        userAgent: details.userAgent,
        sessionId: details.sessionId
      });

      await logEntry.save();

      // Also log to file system for backup
      await this.logToFile({
        userId,
        action,
        details,
        severity,
        timestamp: new Date().toISOString()
      });

      logger.info(`Audit: ${action}`, {
        userId,
        action,
        details,
        severity
      });

      return logEntry;
    } catch (error) {
      logger.error('Error creating audit log:', error);
      throw error;
    }
  }

  async logToFile(logData) {
    try {
      const logFile = path.join(
        this.logDir,
        `audit-${new Date().toISOString().split('T')[0]}.json`
      );

      const logLine = JSON.stringify(logData) + '\n';
      await appendFile(logFile, logLine);
    } catch (error) {
      logger.error('Error writing to audit log file:', error);
    }
  }

  async getActivities(query = {}, limit = 50) {
    try {
      const {
        userId,
        action,
        startDate,
        endDate,
        severity,
        ipAddress
      } = query;

      const mongoQuery = {};

      if (userId) mongoQuery.userId = userId;
      if (action) mongoQuery.action = { $regex: action, $options: 'i' };
      if (severity) mongoQuery.severity = severity;
      if (ipAddress) mongoQuery.ipAddress = ipAddress;

      if (startDate && endDate) {
        mongoQuery.timestamp = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      const activities = await AuditLog.find(mongoQuery)
        .populate('userId', 'username email')
        .sort({ timestamp: -1 })
        .limit(limit);

      return activities;
    } catch (error) {
      logger.error('Error fetching activities:', error);
      throw error;
    }
  }

  async getLoginHistory(userId, limit = 20) {
    try {
      const loginHistory = await LoginHistory.find({ userId })
        .sort({ timestamp: -1 })
        .limit(limit);

      return loginHistory;
    } catch (error) {
      logger.error('Error fetching login history:', error);
      throw error;
    }
  }

  async logLogin(userId, ipAddress, userAgent, success, details = {}) {
    try {
      // Log to audit log
      await this.log(userId, success ? 'login_success' : 'login_failure', {
        ipAddress,
        userAgent,
        success,
        ...details
      });

      // Log to login history
      const loginEntry = new LoginHistory({
        userId,
        ipAddress,
        userAgent,
        success,
        timestamp: new Date(),
        details
      });

      await loginEntry.save();

      return loginEntry;
    } catch (error) {
      logger.error('Error logging login attempt:', error);
      throw error;
    }
  }

  async getLogs(filters = {}) {
    try {
      const {
        startDate,
        endDate,
        userId,
        action,
        severity,
        limit = 100,
        page = 1
      } = filters;

      const query = {};

      if (startDate && endDate) {
        query.timestamp = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      if (userId) query.userId = userId;
      if (action) query.action = { $regex: action, $options: 'i' };
      if (severity) query.severity = severity;

      const logs = await AuditLog.find(query)
        .populate('userId', 'username email')
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip((page - 1) * limit);

      const total = await AuditLog.countDocuments(query);

      return {
        logs,
        pagination: {
          total,
          page,
          pages: Math.ceil(total / limit),
          limit
        }
      };
    } catch (error) {
      logger.error('Error fetching logs:', error);
      throw error;
    }
  }

  async logBackupOperation({
    operationType,
    userId,
    backupId,
    details,
    status,
    timestamp = new Date().toISOString()
  }) {
    try {
      const logEntry = {
        operationType,
        userId,
        backupId,
        details,
        status,
        timestamp,
        ipAddress: this.config.getClientIp?.() || 'unknown'
      };

      const logFile = path.join(
        this.logDir,
        `backup-audit-${new Date().toISOString().split('T')[0]}.json`
      );

      const logLine = JSON.stringify(logEntry) + '\n';
      await appendFile(logFile, logLine);

      return logEntry;
    } catch (error) {
      console.error('Failed to log backup operation:', error);
      throw new Error('Failed to log backup operation');
    }
  }

  async getAuditLogs({
    startDate,
    endDate,
    userId,
    operationType,
    status,
    limit = 100,
    offset = 0
  } = {}) {
    try {
      const files = await fs.promises.readdir(this.logDir);
      const logs = [];

      for (const file of files) {
        if (!file.endsWith('.json')) continue;

        const content = await fs.promises.readFile(
          path.join(this.logDir, file),
          'utf8'
        );

        const entries = content
          .trim()
          .split('\n')
          .map(line => JSON.parse(line))
          .filter(entry => {
            const timestamp = new Date(entry.timestamp);
            return (!startDate || timestamp >= new Date(startDate)) &&
                   (!endDate || timestamp <= new Date(endDate)) &&
                   (!userId || entry.userId === userId) &&
                   (!operationType || entry.operationType === operationType) &&
                   (!status || entry.status === status);
          });

        logs.push(...entries);
      }

      // Sort by timestamp descending
      logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      return {
        total: logs.length,
        logs: logs.slice(offset, offset + limit)
      };
    } catch (error) {
      console.error('Failed to retrieve audit logs:', error);
      throw new Error('Failed to retrieve audit logs');
    }
  }

  async exportLogs(format, filters = {}) {
    try {
      const { logs } = await this.getLogs({ ...filters, limit: 10000 });

      switch (format) {
        case 'csv':
          return this.exportToCSV(logs);
        case 'json':
          return this.exportToJSON(logs);
        case 'xml':
          return this.exportToXML(logs);
        default:
          throw new Error('Unsupported export format');
      }
    } catch (error) {
      logger.error('Error exporting logs:', error);
      throw error;
    }
  }

  exportToCSV(logs) {
    const headers = ['Timestamp', 'User ID', 'Username', 'Action', 'Severity', 'IP Address', 'Details'];
    const csvRows = [headers.join(',')];

    logs.forEach(log => {
      const row = [
        log.timestamp.toISOString(),
        log.userId?._id || '',
        log.userId?.username || '',
        log.action,
        log.severity,
        log.ipAddress || '',
        JSON.stringify(log.details).replace(/"/g, '""')
      ];
      csvRows.push(row.join(','));
    });

    return {
      content: csvRows.join('\n'),
      filename: `audit_logs_${new Date().toISOString().split('T')[0]}.csv`,
      contentType: 'text/csv'
    };
  }

  exportToJSON(logs) {
    return {
      content: JSON.stringify(logs, null, 2),
      filename: `audit_logs_${new Date().toISOString().split('T')[0]}.json`,
      contentType: 'application/json'
    };
  }

  exportToXML(logs) {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<audit_logs>\n';

    logs.forEach(log => {
      xml += '  <log>\n';
      xml += `    <timestamp>${log.timestamp.toISOString()}</timestamp>\n`;
      xml += `    <userId>${log.userId?._id || ''}</userId>\n`;
      xml += `    <username>${log.userId?.username || ''}</username>\n`;
      xml += `    <action>${log.action}</action>\n`;
      xml += `    <severity>${log.severity}</severity>\n`;
      xml += `    <ipAddress>${log.ipAddress || ''}</ipAddress>\n`;
      xml += `    <details>${JSON.stringify(log.details)}</details>\n`;
      xml += '  </log>\n';
    });

    xml += '</audit_logs>';

    return {
      content: xml,
      filename: `audit_logs_${new Date().toISOString().split('T')[0]}.xml`,
      contentType: 'application/xml'
    };
  }

  async clearOldLogs(retentionDays = this.retentionPeriod) {
    try {
      // Clear from database
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const dbResult = await AuditLog.deleteMany({
        timestamp: { $lt: cutoffDate }
      });

      // Clear from file system
      const files = await fs.promises.readdir(this.logDir);
      const retentionDate = new Date();
      retentionDate.setDate(retentionDate.getDate() - retentionDays);

      let filesDeleted = 0;
      for (const file of files) {
        if (!file.endsWith('.json')) continue;

        const filePath = path.join(this.logDir, file);
        const stats = await fs.promises.stat(filePath);

        if (stats.mtime < retentionDate) {
          await fs.promises.unlink(filePath);
          filesDeleted++;
        }
      }

      logger.info(`Cleaned up ${dbResult.deletedCount} database records and ${filesDeleted} log files`);
      return { databaseRecords: dbResult.deletedCount, logFiles: filesDeleted };
    } catch (error) {
      logger.error('Failed to clear old audit logs:', error);
      throw new Error('Failed to clear old audit logs');
    }
  }

  async getAuditStatistics(timeRange = '30d') {
    try {
      const startDate = new Date();

      switch (timeRange) {
        case '24h':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      const stats = await AuditLog.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              action: '$action',
              severity: '$severity'
            },
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: '$_id.action',
            severities: {
              $push: {
                severity: '$_id.severity',
                count: '$count'
              }
            },
            totalCount: { $sum: '$count' }
          }
        }
      ]);

      return {
        timeRange,
        statistics: stats,
        totalEvents: stats.reduce((sum, stat) => sum + stat.totalCount, 0)
      };
    } catch (error) {
      logger.error('Error getting audit statistics:', error);
      throw error;
    }
  }
}