import mongoose from 'mongoose';
import crypto from 'crypto';

const apiKeySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  description: {
    type: String,
    maxlength: 500
  },
  keyId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'ak_' + crypto.randomBytes(16).toString('hex');
    }
  },
  keySecret: {
    type: String,
    required: true,
    select: false // Don't include in queries by default
  },
  keyHash: {
    type: String,
    required: true,
    unique: true
  },
  prefix: {
    type: String,
    default: 'ngp_' // NextGenPanel prefix
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'revoked', 'expired'],
    default: 'active'
  },
  type: {
    type: String,
    enum: ['personal', 'application', 'service', 'webhook', 'integration'],
    default: 'personal'
  },
  environment: {
    type: String,
    enum: ['production', 'staging', 'development', 'testing'],
    default: 'production'
  },
  permissions: {
    scopes: [{
      type: String,
      enum: [
        'read', 'write', 'delete', 'admin',
        'users:read', 'users:write', 'users:delete',
        'billing:read', 'billing:write',
        'servers:read', 'servers:write', 'servers:delete',
        'domains:read', 'domains:write', 'domains:delete',
        'databases:read', 'databases:write', 'databases:delete',
        'files:read', 'files:write', 'files:delete',
        'backups:read', 'backups:write',
        'analytics:read',
        'support:read', 'support:write',
        'webhooks:read', 'webhooks:write',
        'api:read', 'api:write'
      ]
    }],
    resources: [String], // Specific resource IDs this key can access
    ipWhitelist: [String], // IP addresses allowed to use this key
    allowedOrigins: [String], // CORS origins for browser-based requests
    rateLimits: {
      requestsPerMinute: {
        type: Number,
        default: 1000
      },
      requestsPerHour: {
        type: Number,
        default: 10000
      },
      requestsPerDay: {
        type: Number,
        default: 100000
      },
      burstLimit: {
        type: Number,
        default: 100
      }
    }
  },
  usage: {
    totalRequests: {
      type: Number,
      default: 0
    },
    successfulRequests: {
      type: Number,
      default: 0
    },
    failedRequests: {
      type: Number,
      default: 0
    },
    lastUsed: Date,
    firstUsed: Date,
    currentPeriodRequests: {
      type: Number,
      default: 0
    },
    currentPeriodStart: {
      type: Date,
      default: Date.now
    },
    bytesTransferred: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    }
  },
  security: {
    lastRotated: Date,
    rotationInterval: {
      type: Number,
      default: 0 // 0 = no auto-rotation, otherwise days
    },
    autoRotate: {
      type: Boolean,
      default: false
    },
    requiresRotation: {
      type: Boolean,
      default: false
    },
    compromised: {
      type: Boolean,
      default: false
    },
    compromisedAt: Date,
    compromisedReason: String,
    suspiciousActivity: [{
      type: String,
      timestamp: {
        type: Date,
        default: Date.now
      },
      details: mongoose.Schema.Types.Mixed
    }],
    accessAttempts: {
      failed: {
        type: Number,
        default: 0
      },
      lastFailedAttempt: Date,
      lockoutUntil: Date
    }
  },
  expiration: {
    expiresAt: Date,
    neverExpires: {
      type: Boolean,
      default: false
    },
    expirationWarningDays: {
      type: Number,
      default: 30
    },
    autoRenew: {
      type: Boolean,
      default: false
    },
    renewalPeriod: {
      type: Number,
      default: 365 // days
    }
  },
  webhook: {
    url: String,
    events: [String], // Events to send to webhook
    secret: String, // For webhook signature verification
    enabled: {
      type: Boolean,
      default: false
    },
    lastDelivery: Date,
    deliveryAttempts: {
      type: Number,
      default: 0
    },
    failedDeliveries: {
      type: Number,
      default: 0
    }
  },
  metadata: {
    application: String,
    version: String,
    userAgent: String,
    createdFrom: String, // IP address where key was created
    tags: [String],
    customFields: mongoose.Schema.Types.Mixed
  },
  audit: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    revokedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    revokedAt: Date,
    revocationReason: String
  }
}, {
  timestamps: true
});

// Indexes
apiKeySchema.index({ keyId: 1 }, { unique: true });
apiKeySchema.index({ keyHash: 1 }, { unique: true });
apiKeySchema.index({ userId: 1, status: 1 });
apiKeySchema.index({ status: 1, 'expiration.expiresAt': 1 });
apiKeySchema.index({ 'usage.lastUsed': -1 });
apiKeySchema.index({ type: 1, environment: 1 });
apiKeySchema.index({ 'permissions.scopes': 1 });

// Virtual for checking if key is expired
apiKeySchema.virtual('isExpired').get(function() {
  return !this.expiration.neverExpires && 
         this.expiration.expiresAt && 
         this.expiration.expiresAt < new Date();
});

// Virtual for checking if key is locked out
apiKeySchema.virtual('isLockedOut').get(function() {
  return this.security.accessAttempts.lockoutUntil && 
         this.security.accessAttempts.lockoutUntil > new Date();
});

// Virtual for masked key display
apiKeySchema.virtual('maskedKey').get(function() {
  return this.prefix + this.keyId.substring(0, 8) + '...' + this.keyId.substring(-4);
});

// Methods
apiKeySchema.methods.generateKey = function() {
  const secret = crypto.randomBytes(32).toString('hex');
  this.keySecret = secret;
  this.keyHash = crypto.createHash('sha256').update(secret).digest('hex');
  return this.prefix + this.keyId + '.' + secret;
};

apiKeySchema.methods.verifyKey = function(providedKey) {
  if (this.status !== 'active' || this.isExpired || this.isLockedOut) {
    return false;
  }
  
  // Extract secret from provided key
  const parts = providedKey.replace(this.prefix, '').split('.');
  if (parts.length !== 2 || parts[0] !== this.keyId) {
    return false;
  }
  
  const providedSecret = parts[1];
  const providedHash = crypto.createHash('sha256').update(providedSecret).digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(this.keyHash, 'hex'),
    Buffer.from(providedHash, 'hex')
  );
};

apiKeySchema.methods.recordUsage = function(success = true, responseTime = 0, bytesTransferred = 0) {
  this.usage.totalRequests += 1;
  this.usage.lastUsed = new Date();
  
  if (!this.usage.firstUsed) {
    this.usage.firstUsed = new Date();
  }
  
  if (success) {
    this.usage.successfulRequests += 1;
  } else {
    this.usage.failedRequests += 1;
  }
  
  this.usage.bytesTransferred += bytesTransferred;
  
  // Update average response time
  if (responseTime > 0) {
    const totalResponseTime = this.usage.averageResponseTime * (this.usage.totalRequests - 1);
    this.usage.averageResponseTime = (totalResponseTime + responseTime) / this.usage.totalRequests;
  }
  
  // Reset current period if needed
  const now = new Date();
  const periodStart = new Date(this.usage.currentPeriodStart);
  const hoursSincePeriodStart = (now - periodStart) / (1000 * 60 * 60);
  
  if (hoursSincePeriodStart >= 24) {
    this.usage.currentPeriodRequests = 1;
    this.usage.currentPeriodStart = now;
  } else {
    this.usage.currentPeriodRequests += 1;
  }
  
  return this.save();
};

apiKeySchema.methods.recordFailedAccess = function() {
  this.security.accessAttempts.failed += 1;
  this.security.accessAttempts.lastFailedAttempt = new Date();
  
  // Lock out after 5 failed attempts
  if (this.security.accessAttempts.failed >= 5) {
    this.security.accessAttempts.lockoutUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
  }
  
  return this.save();
};

apiKeySchema.methods.resetFailedAttempts = function() {
  this.security.accessAttempts.failed = 0;
  this.security.accessAttempts.lastFailedAttempt = null;
  this.security.accessAttempts.lockoutUntil = null;
  return this.save();
};

apiKeySchema.methods.rotate = function() {
  const newKey = this.generateKey();
  this.security.lastRotated = new Date();
  this.security.requiresRotation = false;
  return this.save().then(() => newKey);
};

apiKeySchema.methods.revoke = function(revokedBy, reason) {
  this.status = 'revoked';
  this.audit.revokedBy = revokedBy;
  this.audit.revokedAt = new Date();
  this.audit.revocationReason = reason;
  return this.save();
};

apiKeySchema.methods.checkRateLimit = function() {
  const now = new Date();
  const limits = this.permissions.rateLimits;
  
  // Check daily limit
  if (this.usage.currentPeriodRequests >= limits.requestsPerDay) {
    return { allowed: false, reason: 'Daily limit exceeded' };
  }
  
  // For minute/hour limits, we'd need to implement a more sophisticated
  // rate limiting mechanism with Redis or similar
  
  return { allowed: true };
};

apiKeySchema.methods.hasPermission = function(scope, resource = null) {
  // Check if key has the required scope
  if (!this.permissions.scopes.includes(scope) && !this.permissions.scopes.includes('admin')) {
    return false;
  }
  
  // Check resource-specific permissions
  if (resource && this.permissions.resources.length > 0) {
    return this.permissions.resources.includes(resource);
  }
  
  return true;
};

apiKeySchema.methods.isIPAllowed = function(ipAddress) {
  if (this.permissions.ipWhitelist.length === 0) {
    return true; // No IP restrictions
  }
  
  return this.permissions.ipWhitelist.includes(ipAddress);
};

// Static methods
apiKeySchema.statics.findByKeyId = function(keyId) {
  return this.findOne({ keyId, status: 'active' });
};

apiKeySchema.statics.getActiveKeys = function(userId = null) {
  const query = { status: 'active' };
  if (userId) {
    query.userId = userId;
  }
  return this.find(query).populate('userId', 'username email');
};

apiKeySchema.statics.getExpiringKeys = function(days = 30) {
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + days);
  
  return this.find({
    status: 'active',
    'expiration.neverExpires': false,
    'expiration.expiresAt': { $lte: expirationDate }
  }).populate('userId', 'username email');
};

apiKeySchema.statics.getUsageStatistics = function(timeRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);
  
  return this.aggregate([
    { $match: { 'usage.lastUsed': { $gte: startDate } } },
    {
      $group: {
        _id: '$type',
        totalKeys: { $sum: 1 },
        totalRequests: { $sum: '$usage.totalRequests' },
        avgResponseTime: { $avg: '$usage.averageResponseTime' },
        totalBytesTransferred: { $sum: '$usage.bytesTransferred' }
      }
    }
  ]);
};

// Pre-save middleware
apiKeySchema.pre('save', function(next) {
  if (this.isNew) {
    // Generate key if not already set
    if (!this.keySecret) {
      this.generateKey();
    }
    
    // Set expiration if not never expires
    if (!this.expiration.neverExpires && !this.expiration.expiresAt) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + (this.expiration.renewalPeriod || 365));
      this.expiration.expiresAt = expirationDate;
    }
  }
  
  // Auto-expire if past expiration date
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
  }
  
  next();
});

export const APIKey = mongoose.model('APIKey', apiKeySchema);
