import { logger } from '../utils/logger.js';

/**
 * Role-based access control middleware
 * Checks if the authenticated user has the required role
 */
export const requireRole = (requiredRole) => {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      // Check if user has the required role
      if (!hasRole(req.user, requiredRole)) {
        logger.warn(`Access denied for user ${req.user.id}: required role '${requiredRole}', user roles: ${req.user.roles?.join(', ') || 'none'}`);
        
        return res.status(403).json({ 
          error: `Access denied. Required role: ${requiredRole}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredRole,
          userRoles: req.user.roles || []
        });
      }

      // User has required role, proceed
      next();
    } catch (error) {
      logger.error('Error in role middleware:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

/**
 * Multiple roles middleware - user must have at least one of the specified roles
 */
export const requireAnyRole = (roles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const hasAnyRole = roles.some(role => hasRole(req.user, role));
      
      if (!hasAnyRole) {
        logger.warn(`Access denied for user ${req.user.id}: required any of roles [${roles.join(', ')}], user roles: ${req.user.roles?.join(', ') || 'none'}`);
        
        return res.status(403).json({ 
          error: `Access denied. Required any of roles: ${roles.join(', ')}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredRoles: roles,
          userRoles: req.user.roles || []
        });
      }

      next();
    } catch (error) {
      logger.error('Error in multiple roles middleware:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

/**
 * All roles middleware - user must have all specified roles
 */
export const requireAllRoles = (roles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const hasAllRoles = roles.every(role => hasRole(req.user, role));
      
      if (!hasAllRoles) {
        const missingRoles = roles.filter(role => !hasRole(req.user, role));
        
        logger.warn(`Access denied for user ${req.user.id}: missing roles [${missingRoles.join(', ')}], user roles: ${req.user.roles?.join(', ') || 'none'}`);
        
        return res.status(403).json({ 
          error: `Access denied. Missing roles: ${missingRoles.join(', ')}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredRoles: roles,
          missingRoles,
          userRoles: req.user.roles || []
        });
      }

      next();
    } catch (error) {
      logger.error('Error in all roles middleware:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

/**
 * Permission-based access control middleware
 */
export const requirePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      if (!hasPermission(req.user, permission)) {
        logger.warn(`Access denied for user ${req.user.id}: required permission '${permission}', user permissions: ${req.user.permissions?.join(', ') || 'none'}`);
        
        return res.status(403).json({ 
          error: `Access denied. Required permission: ${permission}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredPermission: permission,
          userPermissions: req.user.permissions || []
        });
      }

      next();
    } catch (error) {
      logger.error('Error in permission middleware:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * Resource ownership middleware - checks if user owns the resource
 */
export const requireOwnership = (resourceIdParam = 'id') => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const resourceId = req.params[resourceIdParam];
      
      // Admin users can access any resource
      if (hasRole(req.user, 'admin')) {
        return next();
      }

      // Check if user owns the resource (this would typically involve a database lookup)
      // For now, we'll check if the resource ID matches the user ID or if it's in user's owned resources
      if (req.user.id !== resourceId && !req.user.ownedResources?.includes(resourceId)) {
        logger.warn(`Access denied for user ${req.user.id}: not owner of resource ${resourceId}`);
        
        return res.status(403).json({ 
          error: 'Access denied. You can only access your own resources.',
          code: 'RESOURCE_ACCESS_DENIED',
          resourceId
        });
      }

      next();
    } catch (error) {
      logger.error('Error in ownership middleware:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        code: 'OWNERSHIP_CHECK_ERROR'
      });
    }
  };
};

/**
 * Helper function to check if user has a specific role
 */
function hasRole(user, role) {
  if (!user || !user.roles) return false;
  
  // Handle both string and array roles
  const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
  
  // Check for exact role match
  if (userRoles.includes(role)) return true;
  
  // Check for admin role (admin has access to everything)
  if (userRoles.includes('admin')) return true;
  
  // Check for role hierarchy
  return checkRoleHierarchy(userRoles, role);
}

/**
 * Helper function to check if user has a specific permission
 */
function hasPermission(user, permission) {
  if (!user) return false;
  
  // Check direct permissions
  if (user.permissions && user.permissions.includes(permission)) return true;
  
  // Check role-based permissions
  if (user.roles) {
    const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
    return userRoles.some(role => getRolePermissions(role).includes(permission));
  }
  
  return false;
}

/**
 * Role hierarchy check
 */
function checkRoleHierarchy(userRoles, requiredRole) {
  const roleHierarchy = {
    'admin': ['admin', 'developer', 'reseller', 'user'],
    'developer': ['developer', 'user'],
    'reseller': ['reseller', 'user'],
    'user': ['user']
  };

  return userRoles.some(userRole => {
    const allowedRoles = roleHierarchy[userRole] || [];
    return allowedRoles.includes(requiredRole);
  });
}

/**
 * Get permissions for a specific role
 */
function getRolePermissions(role) {
  const rolePermissions = {
    'admin': [
      'read', 'write', 'delete', 'manage_users', 'manage_settings', 
      'manage_billing', 'manage_infrastructure', 'manage_security',
      'deploy', 'manage_team', 'view_analytics', 'manage_backups'
    ],
    'developer': [
      'read', 'write', 'deploy', 'manage_team', 'view_analytics',
      'manage_settings', 'manage_backups'
    ],
    'reseller': [
      'read', 'write', 'manage_users', 'manage_billing', 'view_analytics'
    ],
    'user': [
      'read', 'write'
    ]
  };

  return rolePermissions[role] || [];
}

/**
 * Middleware to add user roles and permissions to request
 */
export const enrichUserContext = async (req, res, next) => {
  try {
    if (req.user) {
      // In a real application, you would fetch user roles and permissions from database
      // For now, we'll use mock data or what's already in the user object
      
      if (!req.user.roles) {
        req.user.roles = ['user']; // Default role
      }
      
      if (!req.user.permissions) {
        // Calculate permissions based on roles
        const userRoles = Array.isArray(req.user.roles) ? req.user.roles : [req.user.roles];
        req.user.permissions = userRoles.reduce((perms, role) => {
          return [...perms, ...getRolePermissions(role)];
        }, []);
        
        // Remove duplicates
        req.user.permissions = [...new Set(req.user.permissions)];
      }
    }
    
    next();
  } catch (error) {
    logger.error('Error enriching user context:', error);
    next(); // Continue even if enrichment fails
  }
};

export default {
  requireRole,
  requireAnyRole,
  requireAllRoles,
  requirePermission,
  requireOwnership,
  enrichUserContext
};
