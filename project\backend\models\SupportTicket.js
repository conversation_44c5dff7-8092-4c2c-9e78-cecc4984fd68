import mongoose from 'mongoose';

const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  isInternal: {
    type: Boolean,
    default: false
  },
  attachments: [{
    filename: String,
    originalName: String,
    mimeType: String,
    size: Number,
    path: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  timestamp: {
    type: Date,
    default: Date.now
  },
  editedAt: Date,
  editedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
});

const supportTicketSchema = new mongoose.Schema({
  ticketNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'TKT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
    }
  },
  // Keep legacy ticketId for backward compatibility
  ticketId: {
    type: String,
    unique: true,
    sparse: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  // Keep legacy subject for backward compatibility
  subject: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 5000
  },
  category: {
    type: String,
    required: true,
    enum: [
      'technical_support', 'billing_inquiry', 'feature_request', 'bug_report',
      'account_issue', 'security_concern', 'performance_issue', 'integration_help',
      'general_inquiry', 'complaint', 'compliment', 'other'
    ]
  },
  priority: {
    type: String,
    required: true,
    enum: ['low', 'normal', 'high', 'urgent', 'critical'],
    default: 'normal'
  },
  severity: {
    type: String,
    enum: ['minor', 'moderate', 'major', 'critical'],
    default: 'moderate'
  },
  status: {
    type: String,
    required: true,
    enum: [
      'open', 'in_progress', 'pending_customer', 'pending_internal',
      'escalated', 'resolved', 'closed', 'cancelled',
      // Legacy statuses for backward compatibility
      'in-progress', 'waiting'
    ],
    default: 'open'
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Keep legacy customer for backward compatibility
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTeam: {
    type: String,
    enum: ['support', 'technical', 'billing', 'security', 'development', 'management']
  },
  tags: [String],
  messages: [messageSchema],
  resolution: {
    summary: String,
    steps: [String],
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: Date,
    resolutionTime: Number, // in minutes
    customerSatisfaction: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      feedback: String,
      submittedAt: Date
    }
  },
  escalation: {
    escalatedAt: Date,
    escalatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    escalatedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String,
    level: {
      type: Number,
      default: 1
    }
  },
  sla: {
    responseTime: {
      target: Number, // in minutes
      actual: Number,
      met: Boolean
    },
    resolutionTime: {
      target: Number, // in minutes
      actual: Number,
      met: Boolean
    },
    breached: {
      type: Boolean,
      default: false
    },
    breachReason: String
  },
  metrics: {
    firstResponseTime: Number, // in minutes
    totalResponseTime: Number,
    customerResponseCount: {
      type: Number,
      default: 0
    },
    agentResponseCount: {
      type: Number,
      default: 0
    },
    reopenCount: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    }
  },
  relatedTickets: [{
    ticketId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SupportTicket'
    },
    relationship: {
      type: String,
      enum: ['duplicate', 'related', 'parent', 'child', 'blocks', 'blocked_by']
    }
  }],
  customerInfo: {
    email: String,
    phone: String,
    company: String,
    plan: String,
    accountValue: Number,
    previousTickets: Number,
    satisfactionHistory: Number
  },
  source: {
    type: String,
    enum: ['web', 'email', 'phone', 'chat', 'api', 'social_media'],
    default: 'web'
  },
  language: {
    type: String,
    default: 'en'
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  // Legacy fields for backward compatibility
  updatedAt: {
    type: Date,
    default: Date.now
  },
  resolvedAt: {
    type: Date
  },
  satisfaction: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String
  }
}, {
  timestamps: true
});

// Indexes
supportTicketSchema.index({ ticketNumber: 1 }, { unique: true });
supportTicketSchema.index({ ticketId: 1 }, { unique: true, sparse: true });
supportTicketSchema.index({ submittedBy: 1, createdAt: -1 });
supportTicketSchema.index({ assignedTo: 1, status: 1 });
supportTicketSchema.index({ status: 1, priority: 1, createdAt: -1 });
supportTicketSchema.index({ category: 1, status: 1 });
supportTicketSchema.index({ tags: 1 });
supportTicketSchema.index({ 'metrics.lastActivity': -1 });
supportTicketSchema.index({ 'sla.breached': 1, status: 1 });

// Virtual for age in hours
supportTicketSchema.virtual('ageInHours').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60));
});

// Virtual for time since last activity
supportTicketSchema.virtual('timeSinceLastActivity').get(function() {
  return Math.floor((Date.now() - this.metrics.lastActivity.getTime()) / (1000 * 60));
});

// Enhanced methods
supportTicketSchema.methods.addComment = function(authorId, content, isInternal = false, attachments = []) {
  this.messages.push({
    sender: authorId,
    content,
    isInternal,
    attachments,
    timestamp: new Date()
  });

  this.metrics.lastActivity = new Date();
  if (isInternal) {
    this.metrics.agentResponseCount += 1;
  } else {
    this.metrics.customerResponseCount += 1;
  }

  return this.save();
};

supportTicketSchema.methods.assignTo = function(userId, teamName = null) {
  this.assignedTo = userId;
  if (teamName) {
    this.assignedTeam = teamName;
  }
  this.metrics.lastActivity = new Date();
  return this.save();
};

supportTicketSchema.methods.updateTicketStatus = function(newStatus, userId) {
  const oldStatus = this.status;
  this.status = newStatus;
  this.metrics.lastActivity = new Date();

  if (newStatus === 'resolved' && oldStatus !== 'resolved') {
    this.resolution.resolvedAt = new Date();
    this.resolution.resolvedBy = userId;
    this.resolution.resolutionTime = Math.floor(
      (this.resolution.resolvedAt - this.createdAt) / (1000 * 60)
    );

    // Legacy field for backward compatibility
    this.resolvedAt = this.resolution.resolvedAt;

    // Check SLA compliance
    if (this.sla.resolutionTime.target) {
      this.sla.resolutionTime.actual = this.resolution.resolutionTime;
      this.sla.resolutionTime.met = this.resolution.resolutionTime <= this.sla.resolutionTime.target;
      if (!this.sla.resolutionTime.met) {
        this.sla.breached = true;
        this.sla.breachReason = 'Resolution time exceeded';
      }
    }
  }

  if (newStatus === 'open' && oldStatus === 'closed') {
    this.metrics.reopenCount += 1;
  }

  return this.save();
};

supportTicketSchema.methods.escalate = function(escalatedBy, escalatedTo, reason, level = null) {
  this.escalation = {
    escalatedAt: new Date(),
    escalatedBy,
    escalatedTo,
    reason,
    level: level || (this.escalation.level || 0) + 1
  };

  this.status = 'escalated';
  this.assignedTo = escalatedTo;
  this.metrics.lastActivity = new Date();

  return this.save();
};

supportTicketSchema.methods.calculateSLA = function() {
  const prioritySLA = {
    'critical': { response: 15, resolution: 240 }, // 15 min, 4 hours
    'urgent': { response: 30, resolution: 480 },   // 30 min, 8 hours
    'high': { response: 60, resolution: 1440 },    // 1 hour, 24 hours
    'normal': { response: 240, resolution: 4320 }, // 4 hours, 3 days
    'low': { response: 480, resolution: 10080 }    // 8 hours, 7 days
  };

  const sla = prioritySLA[this.priority] || prioritySLA.normal;
  this.sla.responseTime.target = sla.response;
  this.sla.resolutionTime.target = sla.resolution;

  return this.save();
};

// Legacy method for backward compatibility
supportTicketSchema.methods.addMessage = async function(sender, content, attachments = []) {
  return this.addComment(sender, content, false, attachments);
};

// Legacy method for backward compatibility
supportTicketSchema.methods.updateStatus = async function(status) {
  return this.updateTicketStatus(status, null);
};

// Static methods
supportTicketSchema.statics.getOpenTickets = function(assignedTo = null) {
  const query = { status: { $in: ['open', 'in_progress', 'pending_customer', 'pending_internal', 'in-progress'] } };
  if (assignedTo) {
    query.assignedTo = assignedTo;
  }
  return this.find(query).sort({ priority: -1, createdAt: 1 });
};

supportTicketSchema.statics.getTicketsByPriority = function(priority) {
  return this.find({
    priority,
    status: { $nin: ['resolved', 'closed', 'cancelled'] }
  }).sort({ createdAt: 1 });
};

supportTicketSchema.statics.getSLABreachedTickets = function() {
  return this.find({
    'sla.breached': true,
    status: { $nin: ['resolved', 'closed', 'cancelled'] }
  }).sort({ createdAt: 1 });
};

supportTicketSchema.statics.getTicketStatistics = function(timeRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);

  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgResolutionTime: { $avg: '$resolution.resolutionTime' }
      }
    }
  ]);
};

// Pre-save middleware
supportTicketSchema.pre('save', async function(next) {
  if (this.isNew) {
    // Generate legacy ticketId for backward compatibility
    if (!this.ticketId) {
      const count = await mongoose.model('SupportTicket').countDocuments();
      this.ticketId = `TICKET-${String(count + 1).padStart(6, '0')}`;
    }

    // Set legacy fields for backward compatibility
    if (!this.subject && this.title) {
      this.subject = this.title;
    }
    if (!this.customer && this.submittedBy) {
      this.customer = this.submittedBy;
    }

    this.calculateSLA();
  }
  this.updatedAt = new Date();
  next();
});

export const SupportTicket = mongoose.model('SupportTicket', supportTicketSchema);