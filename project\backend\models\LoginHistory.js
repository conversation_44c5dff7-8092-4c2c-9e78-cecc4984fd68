import mongoose from 'mongoose';

const loginHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sessionId: {
    type: String,
    required: true,
    unique: true
  },
  loginType: {
    type: String,
    enum: ['password', 'mfa', 'oauth', 'api_key', 'sso', 'impersonation'],
    default: 'password'
  },
  success: {
    type: Boolean,
    required: true
  },
  failureReason: {
    type: String,
    enum: [
      'invalid_credentials',
      'account_locked',
      'account_disabled',
      'mfa_required',
      'mfa_failed',
      'ip_blocked',
      'rate_limited',
      'session_expired',
      'invalid_token',
      'oauth_error',
      'unknown_error'
    ]
  },
  ipAddress: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        // Validate IPv4 and IPv6 addresses
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(v) || ipv6Regex.test(v);
      },
      message: 'Invalid IP address format'
    }
  },
  userAgent: {
    type: String,
    required: true
  },
  device: {
    type: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet', 'unknown'],
      default: 'unknown'
    },
    browser: {
      name: String,
      version: String
    },
    os: {
      name: String,
      version: String
    },
    platform: String,
    isMobile: {
      type: Boolean,
      default: false
    },
    fingerprint: String // Device fingerprint for tracking
  },
  geolocation: {
    country: String,
    countryCode: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    timezone: String,
    isp: String,
    organization: String
  },
  security: {
    riskScore: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    riskFactors: [String],
    isNewDevice: {
      type: Boolean,
      default: false
    },
    isNewLocation: {
      type: Boolean,
      default: false
    },
    isSuspicious: {
      type: Boolean,
      default: false
    },
    vpnDetected: {
      type: Boolean,
      default: false
    },
    proxyDetected: {
      type: Boolean,
      default: false
    },
    torDetected: {
      type: Boolean,
      default: false
    }
  },
  session: {
    duration: Number, // in seconds
    logoutTime: Date,
    logoutType: {
      type: String,
      enum: ['manual', 'timeout', 'forced', 'system'],
      default: 'manual'
    },
    isActive: {
      type: Boolean,
      default: true
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    activityCount: {
      type: Number,
      default: 0
    }
  },
  mfa: {
    required: {
      type: Boolean,
      default: false
    },
    method: {
      type: String,
      enum: ['totp', 'sms', 'email', 'backup_code', 'hardware_key']
    },
    attempts: {
      type: Number,
      default: 0
    },
    success: {
      type: Boolean,
      default: false
    },
    timestamp: Date
  },
  oauth: {
    provider: {
      type: String,
      enum: ['google', 'github', 'microsoft', 'facebook', 'linkedin']
    },
    providerId: String,
    email: String,
    scope: [String]
  },
  api: {
    keyId: String,
    keyName: String,
    permissions: [String]
  },
  impersonation: {
    impersonatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String,
    duration: Number, // in seconds
    endedAt: Date
  },
  alerts: {
    triggered: {
      type: Boolean,
      default: false
    },
    rules: [{
      ruleId: String,
      ruleName: String,
      severity: String,
      message: String
    }],
    notifications: [{
      channel: String,
      recipient: String,
      sentAt: Date,
      status: String
    }]
  },
  metadata: {
    referrer: String,
    source: String, // web, mobile_app, api, etc.
    version: String, // Application version
    environment: {
      type: String,
      enum: ['production', 'staging', 'development'],
      default: 'production'
    },
    tags: [String],
    customFields: mongoose.Schema.Types.Mixed
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  }
}, {
  timestamps: true
});

// Indexes
loginHistorySchema.index({ userId: 1, timestamp: -1 });
loginHistorySchema.index({ sessionId: 1 }, { unique: true });
loginHistorySchema.index({ ipAddress: 1, timestamp: -1 });
loginHistorySchema.index({ success: 1, timestamp: -1 });
loginHistorySchema.index({ 'security.riskScore': -1, timestamp: -1 });
loginHistorySchema.index({ 'security.isSuspicious': 1, timestamp: -1 });
loginHistorySchema.index({ 'session.isActive': 1 });
loginHistorySchema.index({ 'alerts.triggered': 1, timestamp: -1 });
loginHistorySchema.index({ loginType: 1, timestamp: -1 });

// Compound indexes
loginHistorySchema.index({ userId: 1, success: 1, timestamp: -1 });
loginHistorySchema.index({ ipAddress: 1, success: 1, timestamp: -1 });
loginHistorySchema.index({ userId: 1, 'session.isActive': 1 });

// Virtual for session duration in human readable format
loginHistorySchema.virtual('sessionDurationFormatted').get(function() {
  if (!this.session.duration) return null;
  
  const hours = Math.floor(this.session.duration / 3600);
  const minutes = Math.floor((this.session.duration % 3600) / 60);
  const seconds = this.session.duration % 60;
  
  if (hours > 0) return `${hours}h ${minutes}m`;
  if (minutes > 0) return `${minutes}m ${seconds}s`;
  return `${seconds}s`;
});

// Methods
loginHistorySchema.methods.calculateRiskScore = function() {
  let score = 0;
  
  // Base score for failed login
  if (!this.success) score += 30;
  
  // New device factor
  if (this.security.isNewDevice) score += 20;
  
  // New location factor
  if (this.security.isNewLocation) score += 15;
  
  // VPN/Proxy/Tor detection
  if (this.security.vpnDetected) score += 10;
  if (this.security.proxyDetected) score += 15;
  if (this.security.torDetected) score += 25;
  
  // Time-based factors
  const hour = this.timestamp.getHours();
  if (hour < 6 || hour > 22) score += 10; // Outside business hours
  
  // Multiple failed attempts from same IP
  // This would require additional logic to check recent attempts
  
  this.security.riskScore = Math.min(score, 100);
  return this.security.riskScore;
};

loginHistorySchema.methods.shouldTriggerAlert = function() {
  // Alert conditions
  const alertConditions = [
    this.security.riskScore > 70,
    this.security.torDetected,
    !this.success && this.security.isNewDevice,
    this.failureReason === 'account_locked'
  ];
  
  return alertConditions.some(condition => condition);
};

loginHistorySchema.methods.endSession = function(logoutType = 'manual') {
  this.session.isActive = false;
  this.session.logoutTime = new Date();
  this.session.logoutType = logoutType;
  
  if (this.timestamp) {
    this.session.duration = Math.floor(
      (this.session.logoutTime - this.timestamp) / 1000
    );
  }
  
  return this.save();
};

loginHistorySchema.methods.updateActivity = function() {
  this.session.lastActivity = new Date();
  this.session.activityCount += 1;
  return this.save();
};

// Static methods
loginHistorySchema.statics.getByUser = function(userId, limit = 50) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('userId', 'username email')
    .populate('impersonation.impersonatedBy', 'username email');
};

loginHistorySchema.statics.getFailedLogins = function(timeRange = 24, limit = 100) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  return this.find({
    success: false,
    timestamp: { $gte: startTime }
  })
  .sort({ timestamp: -1 })
  .limit(limit)
  .populate('userId', 'username email');
};

loginHistorySchema.statics.getSuspiciousLogins = function(timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  return this.find({
    timestamp: { $gte: startTime },
    $or: [
      { 'security.isSuspicious': true },
      { 'security.riskScore': { $gte: 70 } },
      { 'security.torDetected': true }
    ]
  })
  .sort({ 'security.riskScore': -1, timestamp: -1 })
  .populate('userId', 'username email');
};

loginHistorySchema.statics.getActiveSessions = function(userId = null) {
  const query = { 'session.isActive': true };
  if (userId) query.userId = userId;
  
  return this.find(query)
    .sort({ 'session.lastActivity': -1 })
    .populate('userId', 'username email');
};

loginHistorySchema.statics.getLoginStatistics = function(timeRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);
  
  return this.aggregate([
    { $match: { timestamp: { $gte: startDate } } },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
          success: '$success'
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.date',
        successful: {
          $sum: { $cond: [{ $eq: ['$_id.success', true] }, '$count', 0] }
        },
        failed: {
          $sum: { $cond: [{ $eq: ['$_id.success', false] }, '$count', 0] }
        },
        total: { $sum: '$count' }
      }
    },
    { $sort: { _id: 1 } }
  ]);
};

// Pre-save middleware
loginHistorySchema.pre('save', function(next) {
  if (this.isNew) {
    // Calculate risk score
    this.calculateRiskScore();
    
    // Check if alerts should be triggered
    if (this.shouldTriggerAlert()) {
      this.alerts.triggered = true;
    }
    
    // Mark as suspicious if risk score is high
    if (this.security.riskScore > 70) {
      this.security.isSuspicious = true;
    }
  }
  next();
});

export const LoginHistory = mongoose.model('LoginHistory', loginHistorySchema);
