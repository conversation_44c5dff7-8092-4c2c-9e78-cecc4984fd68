import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

export class InfrastructureService {
  constructor(config = {}) {
    this.config = {
      kubernetes: {
        enabled: config.kubernetes?.enabled || false,
        configPath: config.kubernetes?.configPath || '~/.kube/config',
        namespace: config.kubernetes?.namespace || 'default'
      },
      docker: {
        enabled: config.docker?.enabled || true,
        socketPath: config.docker?.socketPath || '/var/run/docker.sock'
      },
      monitoring: {
        enabled: config.monitoring?.enabled || true,
        interval: config.monitoring?.interval || 30000, // 30 seconds
        metrics: config.monitoring?.metrics || ['cpu', 'memory', 'disk', 'network']
      },
      loadBalancer: {
        type: config.loadBalancer?.type || 'nginx', // nginx, haproxy, aws-alb
        configPath: config.loadBalancer?.configPath || '/etc/nginx/nginx.conf'
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.hostingNodes = new Map();
    this.containerClusters = new Map();
    this.loadBalancers = new Map();
    this.monitoringData = new Map();
    
    this.initializeMonitoring();
  }

  // ===== HOSTING NODES MANAGEMENT =====

  async getHostingNodes() {
    try {
      const nodes = Array.from(this.hostingNodes.values());
      
      // Get real-time status for each node
      for (const node of nodes) {
        node.status = await this.getNodeStatus(node.id);
        node.metrics = await this.getNodeMetrics(node.id);
      }

      return nodes;
    } catch (error) {
      logger.error('Error fetching hosting nodes:', error);
      throw error;
    }
  }

  async addHostingNode(nodeData) {
    try {
      const node = {
        id: this.generateId(),
        name: nodeData.name,
        location: nodeData.location,
        specs: nodeData.specs,
        provider: nodeData.provider,
        ipAddress: nodeData.ipAddress,
        sshConfig: nodeData.sshConfig,
        status: 'initializing',
        createdAt: new Date(),
        addedBy: nodeData.addedBy
      };

      // Initialize node connection
      await this.initializeNode(node);

      this.hostingNodes.set(node.id, node);

      await this.auditService.log(
        nodeData.addedBy,
        'hosting_node_added',
        { nodeId: node.id, name: node.name, location: node.location },
        'info'
      );

      logger.info(`Hosting node added: ${node.name} (${node.id})`);
      return node;
    } catch (error) {
      logger.error('Error adding hosting node:', error);
      throw error;
    }
  }

  async initializeNode(node) {
    try {
      // Test SSH connection
      if (node.sshConfig) {
        await this.testSSHConnection(node);
      }

      // Install required software
      await this.installNodeSoftware(node);

      // Configure monitoring
      await this.setupNodeMonitoring(node);

      node.status = 'active';
      node.initializedAt = new Date();

      logger.info(`Node initialized successfully: ${node.name}`);
    } catch (error) {
      node.status = 'failed';
      node.error = error.message;
      logger.error(`Failed to initialize node ${node.name}:`, error);
      throw error;
    }
  }

  async testSSHConnection(node) {
    try {
      const sshCommand = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${node.sshConfig.user}@${node.ipAddress} 'echo "Connection successful"'`;
      const { stdout } = await execAsync(sshCommand);
      
      if (!stdout.includes('Connection successful')) {
        throw new Error('SSH connection test failed');
      }

      logger.info(`SSH connection successful for node: ${node.name}`);
    } catch (error) {
      logger.error(`SSH connection failed for node ${node.name}:`, error);
      throw error;
    }
  }

  async installNodeSoftware(node) {
    try {
      const installCommands = [
        'sudo apt-get update',
        'sudo apt-get install -y docker.io',
        'sudo systemctl start docker',
        'sudo systemctl enable docker',
        'sudo usermod -aG docker $USER'
      ];

      for (const command of installCommands) {
        const sshCommand = `ssh ${node.sshConfig.user}@${node.ipAddress} '${command}'`;
        await execAsync(sshCommand);
      }

      logger.info(`Software installed on node: ${node.name}`);
    } catch (error) {
      logger.error(`Failed to install software on node ${node.name}:`, error);
      throw error;
    }
  }

  async setupNodeMonitoring(node) {
    try {
      // Install monitoring agent
      const monitoringScript = `
        #!/bin/bash
        # Install node monitoring agent
        curl -sSL https://get.docker.com/ | sh
        docker run -d --name node-exporter \\
          --restart unless-stopped \\
          -p 9100:9100 \\
          prom/node-exporter
      `;

      const sshCommand = `ssh ${node.sshConfig.user}@${node.ipAddress} 'bash -s'`;
      await execAsync(sshCommand, { input: monitoringScript });

      logger.info(`Monitoring setup completed for node: ${node.name}`);
    } catch (error) {
      logger.error(`Failed to setup monitoring for node ${node.name}:`, error);
      throw error;
    }
  }

  async getNodeStatus(nodeId) {
    try {
      const node = this.hostingNodes.get(nodeId);
      if (!node) return 'unknown';

      // Ping test
      const { stdout } = await execAsync(`ping -c 1 ${node.ipAddress}`);
      return stdout.includes('1 received') ? 'online' : 'offline';
    } catch (error) {
      return 'offline';
    }
  }

  async getNodeMetrics(nodeId) {
    try {
      const node = this.hostingNodes.get(nodeId);
      if (!node) return null;

      // Get metrics from node exporter
      const metricsUrl = `http://${node.ipAddress}:9100/metrics`;
      const response = await fetch(metricsUrl);
      const metrics = await response.text();

      return this.parseNodeMetrics(metrics);
    } catch (error) {
      logger.error(`Failed to get metrics for node ${nodeId}:`, error);
      return null;
    }
  }

  parseNodeMetrics(metricsText) {
    // Simplified metrics parsing
    const metrics = {
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 0,
      timestamp: new Date()
    };

    // Parse CPU usage
    const cpuMatch = metricsText.match(/node_cpu_seconds_total{.*mode="idle".*} ([\d.]+)/);
    if (cpuMatch) {
      metrics.cpu = Math.max(0, 100 - parseFloat(cpuMatch[1]));
    }

    // Parse memory usage
    const memTotalMatch = metricsText.match(/node_memory_MemTotal_bytes ([\d.]+)/);
    const memAvailMatch = metricsText.match(/node_memory_MemAvailable_bytes ([\d.]+)/);
    if (memTotalMatch && memAvailMatch) {
      const total = parseFloat(memTotalMatch[1]);
      const available = parseFloat(memAvailMatch[1]);
      metrics.memory = ((total - available) / total) * 100;
    }

    return metrics;
  }

  // ===== WEB SERVER CONFIGURATION =====

  async configureWebServer(serverType, configuration) {
    try {
      switch (serverType) {
        case 'nginx':
          await this.configureNginx(configuration);
          break;
        case 'apache':
          await this.configureApache(configuration);
          break;
        default:
          throw new Error(`Unsupported web server type: ${serverType}`);
      }

      await this.auditService.log(
        null,
        'webserver_configured',
        { serverType, configuration },
        'info'
      );

      logger.info(`Web server configured: ${serverType}`);
    } catch (error) {
      logger.error('Error configuring web server:', error);
      throw error;
    }
  }

  async configureNginx(configuration) {
    try {
      const nginxConfig = this.generateNginxConfig(configuration);
      const configPath = '/etc/nginx/sites-available/nextgenpanel';

      // Write configuration file
      fs.writeFileSync(configPath, nginxConfig);

      // Enable site
      await execAsync(`sudo ln -sf ${configPath} /etc/nginx/sites-enabled/`);

      // Test configuration
      await execAsync('sudo nginx -t');

      // Reload nginx
      await execAsync('sudo systemctl reload nginx');

      logger.info('Nginx configuration updated successfully');
    } catch (error) {
      logger.error('Error configuring Nginx:', error);
      throw error;
    }
  }

  generateNginxConfig(configuration) {
    return `
server {
    listen 80;
    listen [::]:80;
    server_name ${configuration.domain || '_'};

    ${configuration.ssl ? `
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    ssl_certificate ${configuration.ssl.cert};
    ssl_certificate_key ${configuration.ssl.key};
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ` : ''}

    location / {
        proxy_pass http://localhost:${configuration.port || 3000};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    ${configuration.staticPath ? `
    location /static/ {
        alias ${configuration.staticPath}/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    ` : ''}
}
    `.trim();
  }

  // ===== LOAD BALANCER MANAGEMENT =====

  async manageLoadBalancer(action, configuration) {
    try {
      switch (action) {
        case 'create':
          return await this.createLoadBalancer(configuration);
        case 'update':
          return await this.updateLoadBalancer(configuration);
        case 'delete':
          return await this.deleteLoadBalancer(configuration.id);
        default:
          throw new Error(`Unsupported load balancer action: ${action}`);
      }
    } catch (error) {
      logger.error('Error managing load balancer:', error);
      throw error;
    }
  }

  async createLoadBalancer(configuration) {
    try {
      const loadBalancer = {
        id: this.generateId(),
        name: configuration.name,
        type: configuration.type || 'nginx',
        backends: configuration.backends || [],
        healthCheck: configuration.healthCheck || {},
        createdAt: new Date()
      };

      // Generate load balancer configuration
      await this.generateLoadBalancerConfig(loadBalancer);

      this.loadBalancers.set(loadBalancer.id, loadBalancer);

      logger.info(`Load balancer created: ${loadBalancer.name}`);
      return loadBalancer;
    } catch (error) {
      logger.error('Error creating load balancer:', error);
      throw error;
    }
  }

  async generateLoadBalancerConfig(loadBalancer) {
    if (loadBalancer.type === 'nginx') {
      const config = `
upstream ${loadBalancer.name}_backend {
    ${loadBalancer.backends.map(backend => 
      `server ${backend.host}:${backend.port} weight=${backend.weight || 1};`
    ).join('\n    ')}
}

server {
    listen 80;
    server_name ${loadBalancer.name}.local;

    location / {
        proxy_pass http://${loadBalancer.name}_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    ${loadBalancer.healthCheck.path ? `
    location ${loadBalancer.healthCheck.path} {
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }
    ` : ''}
}
      `.trim();

      const configPath = `/etc/nginx/sites-available/${loadBalancer.name}`;
      fs.writeFileSync(configPath, config);

      await execAsync(`sudo ln -sf ${configPath} /etc/nginx/sites-enabled/`);
      await execAsync('sudo nginx -t && sudo systemctl reload nginx');
    }
  }

  // ===== CONTAINER CLUSTERS =====

  async getContainerClusters() {
    try {
      if (this.config.kubernetes.enabled) {
        return await this.getKubernetesClusters();
      } else {
        return await this.getDockerContainers();
      }
    } catch (error) {
      logger.error('Error fetching container clusters:', error);
      throw error;
    }
  }

  async getKubernetesClusters() {
    try {
      const { stdout } = await execAsync('kubectl get nodes -o json');
      const nodes = JSON.parse(stdout);

      const clusters = nodes.items.map(node => ({
        id: node.metadata.name,
        name: node.metadata.name,
        status: node.status.conditions.find(c => c.type === 'Ready')?.status === 'True' ? 'ready' : 'not-ready',
        version: node.status.nodeInfo.kubeletVersion,
        capacity: node.status.capacity,
        allocatable: node.status.allocatable
      }));

      return clusters;
    } catch (error) {
      logger.error('Error fetching Kubernetes clusters:', error);
      throw error;
    }
  }

  async getDockerContainers() {
    try {
      const { stdout } = await execAsync('docker ps --format "{{.ID}},{{.Names}},{{.Status}},{{.Image}}"');
      const containers = stdout.trim().split('\n').map(line => {
        const [id, name, status, image] = line.split(',');
        return { id, name, status, image };
      });

      return containers;
    } catch (error) {
      logger.error('Error fetching Docker containers:', error);
      throw error;
    }
  }

  // ===== MONITORING =====

  initializeMonitoring() {
    if (this.config.monitoring.enabled) {
      setInterval(() => {
        this.collectSystemMetrics();
      }, this.config.monitoring.interval);
    }
  }

  async collectSystemMetrics() {
    try {
      const metrics = {
        timestamp: new Date(),
        cpu: await this.getCPUUsage(),
        memory: await this.getMemoryUsage(),
        disk: await this.getDiskUsage(),
        network: await this.getNetworkUsage()
      };

      this.monitoringData.set('system', metrics);
    } catch (error) {
      logger.error('Error collecting system metrics:', error);
    }
  }

  async getCPUUsage() {
    try {
      const { stdout } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
      return parseFloat(stdout.trim());
    } catch (error) {
      return 0;
    }
  }

  async getMemoryUsage() {
    try {
      const { stdout } = await execAsync("free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'");
      return parseFloat(stdout.trim());
    } catch (error) {
      return 0;
    }
  }

  async getDiskUsage() {
    try {
      const { stdout } = await execAsync("df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1");
      return parseFloat(stdout.trim());
    } catch (error) {
      return 0;
    }
  }

  async getNetworkUsage() {
    try {
      // Simplified network usage calculation
      const { stdout } = await execAsync("cat /proc/net/dev | grep eth0 | awk '{print $2,$10}'");
      const [rx, tx] = stdout.trim().split(' ').map(Number);
      return { rx, tx };
    } catch (error) {
      return { rx: 0, tx: 0 };
    }
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
