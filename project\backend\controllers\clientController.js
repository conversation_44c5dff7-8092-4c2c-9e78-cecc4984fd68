import { ClientService } from '../services/clientService.js';
import { Client } from '../models/Client.js';
import { SupportTicket } from '../models/SupportTicket.js';
import { logger } from '../utils/logger.js';

const clientService = new ClientService();

// ===== DASHBOARD =====
export const getClientDashboard = async (req, res) => {
  try {
    const client = await clientService.getClientProfile(req.user.id);

    const dashboardData = {
      profile: {
        name: client.profile.businessName || `${req.user.firstName} ${req.user.lastName}`,
        plan: client.subscription.plan,
        status: client.subscription.status,
        nextBilling: client.subscription.endDate
      },
      websites: {
        total: client.websites.length,
        active: client.websites.filter(w => w.status === 'active').length,
        list: client.websites.slice(0, 5) // Show first 5
      },
      resources: {
        storage: {
          used: client.totalStorageUsage,
          allocated: client.resources.allocated.storage * 1024, // Convert GB to MB
          percentage: client.storageUsagePercentage
        },
        bandwidth: {
          used: client.resources.usage.bandwidth.used,
          allocated: client.resources.allocated.bandwidth * 1024,
          percentage: (client.resources.usage.bandwidth.used / (client.resources.allocated.bandwidth * 1024)) * 100
        },
        domains: {
          used: client.resources.usage.domains.used,
          allocated: client.resources.allocated.domains
        },
        emails: {
          used: client.resources.usage.emailAccounts.used,
          allocated: client.resources.allocated.emailAccounts
        }
      },
      recentActivity: await getRecentActivity(client),
      alerts: await getActiveAlerts(client),
      uptime: await getOverallUptime(client)
    };

    res.json(dashboardData);
  } catch (error) {
    logger.error('Error getting client dashboard:', error);
    res.status(500).json({ error: error.message });
  }
};

// ===== PROFILE MANAGEMENT =====
export const getClientProfile = async (req, res) => {
  try {
    const client = await clientService.getClientProfile(req.user.id);
    res.json(client);
  } catch (error) {
    logger.error('Error getting client profile:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updateClientProfile = async (req, res) => {
  try {
    const client = await clientService.updateClientProfile(req.user.id, req.body);
    res.json(client);
  } catch (error) {
    logger.error('Error updating client profile:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== WEBSITE MANAGEMENT =====
export const getWebsites = async (req, res) => {
  try {
    const websites = await clientService.getWebsites(req.user.id);
    res.json({ websites });
  } catch (error) {
    logger.error('Error getting websites:', error);
    res.status(500).json({ error: error.message });
  }
};

export const addWebsite = async (req, res) => {
  try {
    const website = await clientService.addWebsite(req.user.id, req.body);
    res.status(201).json(website);
  } catch (error) {
    logger.error('Error adding website:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getWebsiteDetails = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const website = client.websites.id(req.params.websiteId);
    if (!website) {
      return res.status(404).json({ error: 'Website not found' });
    }

    // Enrich with real-time data
    const enrichedWebsite = {
      ...website.toObject(),
      uptime: await clientService.getWebsiteUptime(website.domain),
      sslStatus: await clientService.getSSLStatus(website.domain),
      securityScan: await clientService.getLatestSecurityScan(website.domain),
      analytics: await clientService.getWebsiteAnalytics(req.user.id, website._id, '7d')
    };

    res.json(enrichedWebsite);
  } catch (error) {
    logger.error('Error getting website details:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updateWebsite = async (req, res) => {
  try {
    const website = await clientService.updateWebsite(req.user.id, req.params.websiteId, req.body);
    res.json(website);
  } catch (error) {
    logger.error('Error updating website:', error);
    res.status(400).json({ error: error.message });
  }
};

export const deleteWebsite = async (req, res) => {
  try {
    await clientService.deleteWebsite(req.user.id, req.params.websiteId);
    res.json({ success: true });
  } catch (error) {
    logger.error('Error deleting website:', error);
    res.status(400).json({ error: error.message });
  }
};

export const toggleWebsiteStatus = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const website = client.websites.id(req.params.websiteId);
    if (!website) {
      return res.status(404).json({ error: 'Website not found' });
    }

    website.status = website.status === 'active' ? 'inactive' : 'active';
    website.lastUpdated = new Date();
    await client.save();

    res.json({ success: true, status: website.status });
  } catch (error) {
    logger.error('Error toggling website status:', error);
    res.status(500).json({ error: error.message });
  }
};

export const clearWebsiteCache = async (req, res) => {
  try {
    // Mock cache clearing
    res.json({
      success: true,
      message: 'Website cache cleared successfully',
      clearedAt: new Date()
    });
  } catch (error) {
    logger.error('Error clearing website cache:', error);
    res.status(500).json({ error: error.message });
  }
};

// ===== ANALYTICS & REPORTING =====
export const getWebsiteAnalytics = async (req, res) => {
  try {
    const timeRange = req.query.timeRange || '30d';
    const analytics = await clientService.getWebsiteAnalytics(req.user.id, req.params.websiteId, timeRange);
    res.json(analytics);
  } catch (error) {
    logger.error('Error getting website analytics:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getTrafficReport = async (req, res) => {
  try {
    const report = await clientService.generateReport(req.user.id, 'traffic', req.query);
    res.json(report);
  } catch (error) {
    logger.error('Error getting traffic report:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getPerformanceReport = async (req, res) => {
  try {
    const report = await clientService.generateReport(req.user.id, 'performance', req.query);
    res.json(report);
  } catch (error) {
    logger.error('Error getting performance report:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getSecurityReport = async (req, res) => {
  try {
    const report = await clientService.generateReport(req.user.id, 'security', req.query);
    res.json(report);
  } catch (error) {
    logger.error('Error getting security report:', error);
    res.status(500).json({ error: error.message });
  }
};

export const downloadReport = async (req, res) => {
  try {
    // Mock report download
    res.json({
      downloadUrl: `/api/client/reports/${req.params.reportId}/file`,
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error downloading report:', error);
    res.status(500).json({ error: error.message });
  }
};

// ===== FILE & MEDIA MANAGEMENT =====
export const getFileManager = async (req, res) => {
  try {
    const path = req.query.path || '/';

    // Mock file listing
    res.json({
      currentPath: path,
      files: [
        {
          name: 'index.html',
          type: 'file',
          size: 2048,
          modified: new Date(),
          permissions: 'rw-r--r--'
        },
        {
          name: 'assets',
          type: 'directory',
          size: null,
          modified: new Date(),
          permissions: 'rwxr-xr-x'
        },
        {
          name: 'images',
          type: 'directory',
          size: null,
          modified: new Date(),
          permissions: 'rwxr-xr-x'
        }
      ],
      totalSize: 15728640, // bytes
      permissions: {
        read: true,
        write: true,
        delete: true,
        upload: true
      }
    });
  } catch (error) {
    logger.error('Error getting file manager:', error);
    res.status(500).json({ error: error.message });
  }
};

export const uploadFile = async (req, res) => {
  try {
    // Mock file upload
    res.json({
      success: true,
      file: {
        name: req.body.filename || 'uploaded-file.txt',
        size: req.body.size || 1024,
        path: req.body.path || '/',
        uploadedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error uploading file:', error);
    res.status(400).json({ error: error.message });
  }
};

export const downloadFile = async (req, res) => {
  try {
    const filePath = req.params[0];

    // Mock file download
    res.json({
      downloadUrl: `/api/client/websites/${req.params.websiteId}/files/download/${filePath}`,
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error downloading file:', error);
    res.status(500).json({ error: error.message });
  }
};

export const deleteFile = async (req, res) => {
  try {
    const filePath = req.params[0];

    // Mock file deletion
    res.json({
      success: true,
      message: `File ${filePath} deleted successfully`
    });
  } catch (error) {
    logger.error('Error deleting file:', error);
    res.status(400).json({ error: error.message });
  }
};

export const createFolder = async (req, res) => {
  try {
    const { name, path } = req.body;

    // Mock folder creation
    res.json({
      success: true,
      folder: {
        name,
        path: `${path}/${name}`,
        createdAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error creating folder:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== EMAIL MANAGEMENT =====
export const getEmailAccounts = async (req, res) => {
  try {
    const emailAccounts = await clientService.getEmailAccounts(req.user.id);
    res.json({ emailAccounts });
  } catch (error) {
    logger.error('Error getting email accounts:', error);
    res.status(500).json({ error: error.message });
  }
};

export const createEmailAccount = async (req, res) => {
  try {
    const emailAccount = await clientService.createEmailAccount(req.user.id, req.body);
    res.status(201).json(emailAccount);
  } catch (error) {
    logger.error('Error creating email account:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateEmailAccount = async (req, res) => {
  try {
    const emailAccount = await clientService.updateEmailAccount(req.user.id, req.params.emailId, req.body);
    res.json(emailAccount);
  } catch (error) {
    logger.error('Error updating email account:', error);
    res.status(400).json({ error: error.message });
  }
};

export const deleteEmailAccount = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    client.emailAccounts.id(req.params.emailId).remove();
    client.resources.usage.emailAccounts.used -= 1;
    client.resources.usage.emailAccounts.active -= 1;

    await client.save();
    res.json({ success: true });
  } catch (error) {
    logger.error('Error deleting email account:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getWebmailAccess = async (req, res) => {
  try {
    // Mock webmail access
    res.json({
      webmailUrl: 'https://webmail.example.com',
      accessToken: 'webmail_token_' + Date.now(),
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error getting webmail access:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updateEmailSettings = async (req, res) => {
  try {
    // Mock email settings update
    res.json({
      success: true,
      settings: req.body
    });
  } catch (error) {
    logger.error('Error updating email settings:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== BILLING & SUBSCRIPTION =====
export const getBillingInfo = async (req, res) => {
  try {
    const billingInfo = await clientService.getBillingInfo(req.user.id);
    res.json(billingInfo);
  } catch (error) {
    logger.error('Error getting billing info:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getInvoices = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ invoices: client.billing.invoices || [] });
  } catch (error) {
    logger.error('Error getting invoices:', error);
    res.status(500).json({ error: error.message });
  }
};

export const downloadInvoice = async (req, res) => {
  try {
    // Mock invoice download
    res.json({
      downloadUrl: `/api/client/billing/invoices/${req.params.invoiceId}/file`,
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error downloading invoice:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updatePaymentMethod = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    client.billing.paymentMethod = {
      type: req.body.type,
      last4: req.body.last4,
      expiryMonth: req.body.expiryMonth,
      expiryYear: req.body.expiryYear,
      brand: req.body.brand
    };

    await client.save();
    res.json({ success: true, paymentMethod: client.billing.paymentMethod });
  } catch (error) {
    logger.error('Error updating payment method:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateSubscription = async (req, res) => {
  try {
    const subscription = await clientService.updateSubscription(req.user.id, req.body);
    res.json(subscription);
  } catch (error) {
    logger.error('Error updating subscription:', error);
    res.status(400).json({ error: error.message });
  }
};

export const applyPromoCode = async (req, res) => {
  try {
    // Mock promo code application
    res.json({
      success: true,
      discount: {
        code: req.body.code,
        amount: 10.00,
        type: 'percentage',
        description: '10% off for 3 months'
      }
    });
  } catch (error) {
    logger.error('Error applying promo code:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== SUPPORT & HELPDESK =====
export const getSupportTickets = async (req, res) => {
  try {
    const tickets = await clientService.getSupportTickets(req.user.id);
    res.json({ tickets });
  } catch (error) {
    logger.error('Error getting support tickets:', error);
    res.status(500).json({ error: error.message });
  }
};

export const createSupportTicket = async (req, res) => {
  try {
    const ticket = await clientService.createSupportTicket(req.user.id, req.body);
    res.status(201).json(ticket);
  } catch (error) {
    logger.error('Error creating support ticket:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateSupportTicket = async (req, res) => {
  try {
    const ticket = await SupportTicket.findOne({
      ticketId: req.params.ticketId,
      user: req.user.id
    });

    if (!ticket) {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    // Add message to ticket
    if (req.body.message) {
      ticket.messages.push({
        from: req.user.id,
        message: req.body.message,
        timestamp: new Date(),
        attachments: req.body.attachments || []
      });
      ticket.lastUpdate = new Date();
    }

    await ticket.save();
    res.json(ticket);
  } catch (error) {
    logger.error('Error updating support ticket:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getSupportTicketDetails = async (req, res) => {
  try {
    const ticket = await SupportTicket.findOne({
      ticketId: req.params.ticketId,
      user: req.user.id
    }).populate('assignedTo', 'firstName lastName email');

    if (!ticket) {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.json(ticket);
  } catch (error) {
    logger.error('Error getting support ticket details:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getKnowledgeBase = async (req, res) => {
  try {
    // Mock knowledge base
    res.json({
      categories: [
        {
          name: 'Getting Started',
          articles: [
            { title: 'How to add your first website', slug: 'add-first-website', views: 1250 },
            { title: 'Understanding your dashboard', slug: 'dashboard-overview', views: 890 },
            { title: 'Setting up email accounts', slug: 'setup-email', views: 567 }
          ]
        },
        {
          name: 'Website Management',
          articles: [
            { title: 'Uploading files via File Manager', slug: 'file-upload', views: 445 },
            { title: 'SSL certificate setup', slug: 'ssl-setup', views: 334 },
            { title: 'Domain configuration', slug: 'domain-config', views: 223 }
          ]
        },
        {
          name: 'Billing & Subscriptions',
          articles: [
            { title: 'How to upgrade your plan', slug: 'upgrade-plan', views: 667 },
            { title: 'Understanding your invoice', slug: 'invoice-explanation', views: 445 },
            { title: 'Payment methods and billing', slug: 'payment-methods', views: 334 }
          ]
        }
      ],
      popularArticles: [
        { title: 'How to add your first website', slug: 'add-first-website', views: 1250 },
        { title: 'Understanding your dashboard', slug: 'dashboard-overview', views: 890 },
        { title: 'How to upgrade your plan', slug: 'upgrade-plan', views: 667 }
      ]
    });
  } catch (error) {
    logger.error('Error getting knowledge base:', error);
    res.status(500).json({ error: error.message });
  }
};

// ===== SECURITY & PRIVACY =====
export const getSecuritySettings = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({
      security: client.security,
      websites: client.websites.map(w => ({
        id: w._id,
        name: w.name,
        domain: w.domain,
        security: w.security
      }))
    });
  } catch (error) {
    logger.error('Error getting security settings:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updateSecuritySettings = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    Object.assign(client.security, req.body);
    await client.save();

    res.json({ success: true, security: client.security });
  } catch (error) {
    logger.error('Error updating security settings:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getSecurityScore = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Calculate overall security score
    let totalScore = 0;
    let websiteCount = 0;

    for (const website of client.websites) {
      if (website.security.securityScore) {
        totalScore += website.security.securityScore;
        websiteCount++;
      }
    }

    const overallScore = websiteCount > 0 ? Math.round(totalScore / websiteCount) : 0;

    res.json({
      overallScore,
      websites: client.websites.map(w => ({
        id: w._id,
        name: w.name,
        domain: w.domain,
        score: w.security.securityScore || 0,
        lastScan: w.security.lastScan
      })),
      recommendations: [
        'Enable two-factor authentication',
        'Update all website passwords',
        'Enable SSL for all domains',
        'Configure firewall rules'
      ]
    });
  } catch (error) {
    logger.error('Error getting security score:', error);
    res.status(500).json({ error: error.message });
  }
};

export const requestSecurityScan = async (req, res) => {
  try {
    // Mock security scan request
    res.json({
      success: true,
      scan: {
        id: 'scan_' + Date.now(),
        status: 'initiated',
        estimatedTime: '5-10 minutes',
        websiteId: req.body.websiteId
      }
    });
  } catch (error) {
    logger.error('Error requesting security scan:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getPrivacySettings = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({
      privacy: client.settings.privacy || {
        showInDirectory: false,
        allowIndexing: true,
        cookieConsent: false,
        gdprCompliant: false
      }
    });
  } catch (error) {
    logger.error('Error getting privacy settings:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updatePrivacySettings = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    if (!client.settings.privacy) {
      client.settings.privacy = {};
    }

    Object.assign(client.settings.privacy, req.body);
    await client.save();

    res.json({ success: true, privacy: client.settings.privacy });
  } catch (error) {
    logger.error('Error updating privacy settings:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== REMAINING CONTROLLER METHODS =====

// Form Builder & Lead Management
export const getForms = async (req, res) => {
  try {
    // Mock forms data
    res.json({
      forms: [
        {
          id: 'form_1',
          name: 'Contact Form',
          type: 'contact',
          submissions: 45,
          createdAt: new Date(),
          isActive: true
        },
        {
          id: 'form_2',
          name: 'Newsletter Signup',
          type: 'newsletter',
          submissions: 123,
          createdAt: new Date(),
          isActive: true
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting forms:', error);
    res.status(500).json({ error: error.message });
  }
};

export const createForm = async (req, res) => {
  try {
    // Mock form creation
    res.json({
      success: true,
      form: {
        id: 'form_' + Date.now(),
        name: req.body.name,
        type: req.body.type,
        fields: req.body.fields,
        createdAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error creating form:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateForm = async (req, res) => {
  try {
    // Mock form update
    res.json({
      success: true,
      form: {
        id: req.params.formId,
        ...req.body,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error updating form:', error);
    res.status(400).json({ error: error.message });
  }
};

export const deleteForm = async (req, res) => {
  try {
    // Mock form deletion
    res.json({ success: true });
  } catch (error) {
    logger.error('Error deleting form:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getFormSubmissions = async (req, res) => {
  try {
    // Mock form submissions
    res.json({
      submissions: [
        {
          id: 'sub_1',
          formId: req.params.formId,
          data: {
            name: 'John Doe',
            email: '<EMAIL>',
            message: 'Hello, I am interested in your services.'
          },
          submittedAt: new Date(),
          ip: '***********'
        }
      ],
      total: 1,
      page: 1,
      limit: 20
    });
  } catch (error) {
    logger.error('Error getting form submissions:', error);
    res.status(500).json({ error: error.message });
  }
};

export const exportLeads = async (req, res) => {
  try {
    // Mock lead export
    res.json({
      downloadUrl: '/api/client/leads/export/file',
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error exporting leads:', error);
    res.status(500).json({ error: error.message });
  }
};

// Domain & DNS Management
export const getDomains = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ domains: client.domains || [] });
  } catch (error) {
    logger.error('Error getting domains:', error);
    res.status(500).json({ error: error.message });
  }
};

export const getDomainDetails = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const domain = client.domains.id(req.params.domainId);
    if (!domain) {
      return res.status(404).json({ error: 'Domain not found' });
    }

    res.json(domain);
  } catch (error) {
    logger.error('Error getting domain details:', error);
    res.status(500).json({ error: error.message });
  }
};

export const requestDomainChange = async (req, res) => {
  try {
    // Mock domain change request
    res.json({
      success: true,
      request: {
        id: 'req_' + Date.now(),
        type: 'domain_change',
        status: 'pending',
        details: req.body,
        submittedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error requesting domain change:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getDNSRecords = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const domain = client.domains.id(req.params.domainId);
    if (!domain) {
      return res.status(404).json({ error: 'Domain not found' });
    }

    res.json({ dnsRecords: domain.dnsRecords || [] });
  } catch (error) {
    logger.error('Error getting DNS records:', error);
    res.status(500).json({ error: error.message });
  }
};

// Backup & Restore
export const getBackups = async (req, res) => {
  try {
    // Mock backups
    res.json({
      backups: [
        {
          id: 'backup_1',
          type: 'full',
          size: '125MB',
          createdAt: new Date(Date.now() - 86400000), // 1 day ago
          status: 'completed'
        },
        {
          id: 'backup_2',
          type: 'files',
          size: '45MB',
          createdAt: new Date(Date.now() - 172800000), // 2 days ago
          status: 'completed'
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting backups:', error);
    res.status(500).json({ error: error.message });
  }
};

export const requestBackup = async (req, res) => {
  try {
    // Mock backup request
    res.json({
      success: true,
      backup: {
        id: 'backup_' + Date.now(),
        type: req.body.type || 'full',
        status: 'initiated',
        estimatedTime: '10-15 minutes'
      }
    });
  } catch (error) {
    logger.error('Error requesting backup:', error);
    res.status(400).json({ error: error.message });
  }
};

export const requestRestore = async (req, res) => {
  try {
    // Mock restore request
    res.json({
      success: true,
      restore: {
        id: 'restore_' + Date.now(),
        backupId: req.params.backupId,
        status: 'initiated',
        estimatedTime: '15-20 minutes'
      }
    });
  } catch (error) {
    logger.error('Error requesting restore:', error);
    res.status(400).json({ error: error.message });
  }
};

export const downloadBackup = async (req, res) => {
  try {
    // Mock backup download
    res.json({
      downloadUrl: `/api/client/backups/${req.params.backupId}/file`,
      expiresAt: new Date(Date.now() + 3600000) // 1 hour
    });
  } catch (error) {
    logger.error('Error downloading backup:', error);
    res.status(500).json({ error: error.message });
  }
};

// Task & Request Center
export const getTasks = async (req, res) => {
  try {
    // Mock tasks
    res.json({
      tasks: [
        {
          id: 'task_1',
          title: 'Update website design',
          description: 'Please update the homepage design with new branding',
          status: 'pending',
          priority: 'medium',
          assignedTo: 'Developer',
          createdAt: new Date(),
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting tasks:', error);
    res.status(500).json({ error: error.message });
  }
};

export const createTask = async (req, res) => {
  try {
    // Mock task creation
    res.json({
      success: true,
      task: {
        id: 'task_' + Date.now(),
        title: req.body.title,
        description: req.body.description,
        priority: req.body.priority || 'medium',
        status: 'pending',
        createdAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error creating task:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateTask = async (req, res) => {
  try {
    // Mock task update
    res.json({
      success: true,
      task: {
        id: req.params.taskId,
        ...req.body,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error updating task:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getTaskDetails = async (req, res) => {
  try {
    // Mock task details
    res.json({
      id: req.params.taskId,
      title: 'Update website design',
      description: 'Please update the homepage design with new branding',
      status: 'in_progress',
      priority: 'medium',
      assignedTo: 'Developer',
      createdAt: new Date(),
      comments: [
        {
          from: 'Developer',
          message: 'Working on this now, will have it ready by Friday',
          timestamp: new Date()
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting task details:', error);
    res.status(500).json({ error: error.message });
  }
};

export const approveTask = async (req, res) => {
  try {
    // Mock task approval
    res.json({
      success: true,
      message: 'Task approved successfully'
    });
  } catch (error) {
    logger.error('Error approving task:', error);
    res.status(400).json({ error: error.message });
  }
};

// Team Management
export const getTeamMembers = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id }).populate('teamMembers.user', 'firstName lastName email');
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ teamMembers: client.teamMembers || [] });
  } catch (error) {
    logger.error('Error getting team members:', error);
    res.status(500).json({ error: error.message });
  }
};

export const inviteTeamMember = async (req, res) => {
  try {
    // Mock team member invitation
    res.json({
      success: true,
      invitation: {
        id: 'inv_' + Date.now(),
        email: req.body.email,
        role: req.body.role,
        status: 'pending',
        invitedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error inviting team member:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateTeamMember = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const member = client.teamMembers.id(req.params.memberId);
    if (!member) {
      return res.status(404).json({ error: 'Team member not found' });
    }

    Object.assign(member, req.body);
    await client.save();

    res.json({ success: true, member });
  } catch (error) {
    logger.error('Error updating team member:', error);
    res.status(400).json({ error: error.message });
  }
};

export const removeTeamMember = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    client.teamMembers.id(req.params.memberId).remove();
    await client.save();

    res.json({ success: true });
  } catch (error) {
    logger.error('Error removing team member:', error);
    res.status(400).json({ error: error.message });
  }
};

// Notifications & Alerts
export const getNotifications = async (req, res) => {
  try {
    // Mock notifications
    res.json({
      notifications: [
        {
          id: 'notif_1',
          type: 'billing',
          title: 'Payment Due',
          message: 'Your monthly payment is due in 3 days',
          read: false,
          createdAt: new Date()
        },
        {
          id: 'notif_2',
          type: 'security',
          title: 'SSL Certificate Renewed',
          message: 'SSL certificate for example.com has been renewed',
          read: true,
          createdAt: new Date(Date.now() - 86400000)
        }
      ],
      unreadCount: 1
    });
  } catch (error) {
    logger.error('Error getting notifications:', error);
    res.status(500).json({ error: error.message });
  }
};

export const markNotificationRead = async (req, res) => {
  try {
    // Mock notification read
    res.json({ success: true });
  } catch (error) {
    logger.error('Error marking notification as read:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateNotificationSettings = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    Object.assign(client.notifications, req.body);
    await client.save();

    res.json({ success: true, notifications: client.notifications });
  } catch (error) {
    logger.error('Error updating notification settings:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getAlerts = async (req, res) => {
  try {
    // Mock alerts
    res.json({
      alerts: [
        {
          id: 'alert_1',
          type: 'warning',
          title: 'High Storage Usage',
          message: 'You are using 85% of your storage quota',
          severity: 'medium',
          createdAt: new Date()
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting alerts:', error);
    res.status(500).json({ error: error.message });
  }
};

// Marketplace
export const getMarketplace = async (req, res) => {
  try {
    // Mock marketplace
    res.json({
      categories: [
        {
          name: 'Security',
          addons: [
            {
              id: 'addon_1',
              name: 'Advanced Security Suite',
              description: 'Enhanced security features and monitoring',
              price: 9.99,
              rating: 4.8
            }
          ]
        },
        {
          name: 'Performance',
          addons: [
            {
              id: 'addon_2',
              name: 'CDN Pro',
              description: 'Global content delivery network',
              price: 14.99,
              rating: 4.9
            }
          ]
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting marketplace:', error);
    res.status(500).json({ error: error.message });
  }
};

export const purchaseAddon = async (req, res) => {
  try {
    // Mock addon purchase
    res.json({
      success: true,
      purchase: {
        id: 'purchase_' + Date.now(),
        addonId: req.params.addonId,
        status: 'completed',
        purchasedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error purchasing addon:', error);
    res.status(400).json({ error: error.message });
  }
};

export const getInstalledAddons = async (req, res) => {
  try {
    // Mock installed addons
    res.json({
      addons: [
        {
          id: 'addon_1',
          name: 'Advanced Security Suite',
          status: 'active',
          installedAt: new Date()
        }
      ]
    });
  } catch (error) {
    logger.error('Error getting installed addons:', error);
    res.status(500).json({ error: error.message });
  }
};

// Integrations
export const getIntegrations = async (req, res) => {
  try {
    const client = await Client.findOne({ user: req.user.id });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json({ integrations: client.integrations || {} });
  } catch (error) {
    logger.error('Error getting integrations:', error);
    res.status(500).json({ error: error.message });
  }
};

export const connectIntegration = async (req, res) => {
  try {
    // Mock integration connection
    res.json({
      success: true,
      integration: {
        provider: req.params.provider,
        status: 'connected',
        connectedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Error connecting integration:', error);
    res.status(400).json({ error: error.message });
  }
};

export const disconnectIntegration = async (req, res) => {
  try {
    // Mock integration disconnection
    res.json({ success: true });
  } catch (error) {
    logger.error('Error disconnecting integration:', error);
    res.status(400).json({ error: error.message });
  }
};

export const updateIntegrationSettings = async (req, res) => {
  try {
    // Mock integration settings update
    res.json({
      success: true,
      settings: req.body
    });
  } catch (error) {
    logger.error('Error updating integration settings:', error);
    res.status(400).json({ error: error.message });
  }
};

// ===== HELPER FUNCTIONS =====
async function getRecentActivity(client) {
  // Mock recent activity
  return [
    {
      type: 'website_updated',
      message: 'Website updated',
      timestamp: new Date(),
      icon: 'globe'
    }
  ];
}

async function getActiveAlerts(client) {
  // Mock active alerts
  return [
    {
      type: 'warning',
      message: 'Storage usage at 85%',
      severity: 'medium'
    }
  ];
}

async function getOverallUptime(client) {
  // Mock uptime calculation
  return {
    percentage: 99.9,
    status: 'excellent'
  };
}
