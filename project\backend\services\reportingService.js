import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import fs from 'fs';
import path from 'path';

export class ReportingService {
  constructor(config = {}) {
    this.config = {
      outputDir: config.outputDir || path.join(process.cwd(), 'reports'),
      formats: config.formats || ['json', 'csv', 'pdf'],
      retention: config.retention || 90, // days
      scheduling: {
        enabled: config.scheduling?.enabled || true,
        defaultFrequency: config.scheduling?.defaultFrequency || 'weekly'
      },
      ...config
    };

    this.auditService = new AuditLogService();
    this.reports = new Map();
    this.scheduledReports = new Map();
    this.templates = new Map();
    
    this.initializeReporting();
  }

  async initializeReporting() {
    try {
      // Ensure reports directory exists
      if (!fs.existsSync(this.config.outputDir)) {
        fs.mkdirSync(this.config.outputDir, { recursive: true });
      }

      // Load default report templates
      this.loadDefaultTemplates();

      logger.info('Reporting service initialized successfully');
    } catch (error) {
      logger.error('Error initializing reporting service:', error);
    }
  }

  loadDefaultTemplates() {
    // User Analytics Report
    this.templates.set('user_analytics', {
      name: 'User Analytics Report',
      description: 'Comprehensive user activity and engagement metrics',
      dataSource: 'users',
      metrics: [
        'total_users', 'active_users', 'new_registrations', 'user_retention',
        'login_frequency', 'feature_usage', 'geographic_distribution'
      ],
      charts: ['line', 'bar', 'pie'],
      defaultPeriod: 30
    });

    // System Performance Report
    this.templates.set('system_performance', {
      name: 'System Performance Report',
      description: 'Infrastructure and system health metrics',
      dataSource: 'monitoring',
      metrics: [
        'cpu_usage', 'memory_usage', 'disk_usage', 'network_traffic',
        'response_times', 'error_rates', 'uptime'
      ],
      charts: ['line', 'area'],
      defaultPeriod: 7
    });

    // Revenue Analytics Report
    this.templates.set('revenue_analytics', {
      name: 'Revenue Analytics Report',
      description: 'Financial performance and billing metrics',
      dataSource: 'billing',
      metrics: [
        'total_revenue', 'monthly_recurring_revenue', 'customer_lifetime_value',
        'churn_rate', 'conversion_rate', 'payment_success_rate'
      ],
      charts: ['line', 'bar', 'funnel'],
      defaultPeriod: 30
    });

    // Security Report
    this.templates.set('security_report', {
      name: 'Security Report',
      description: 'Security incidents, threats, and compliance status',
      dataSource: 'security',
      metrics: [
        'security_incidents', 'failed_logins', 'blocked_ips', 'vulnerability_scans',
        'compliance_status', 'access_patterns'
      ],
      charts: ['bar', 'heatmap', 'timeline'],
      defaultPeriod: 30
    });

    // Support Analytics Report
    this.templates.set('support_analytics', {
      name: 'Support Analytics Report',
      description: 'Customer support performance and ticket analytics',
      dataSource: 'support',
      metrics: [
        'ticket_volume', 'resolution_time', 'customer_satisfaction',
        'agent_performance', 'escalation_rate', 'first_contact_resolution'
      ],
      charts: ['line', 'bar', 'gauge'],
      defaultPeriod: 30
    });
  }

  async generateReport(templateId, options = {}) {
    try {
      const template = this.templates.get(templateId);
      if (!template) {
        throw new Error(`Report template '${templateId}' not found`);
      }

      const reportId = this.generateReportId();
      const startDate = options.startDate || this.getDefaultStartDate(template.defaultPeriod);
      const endDate = options.endDate || new Date();
      const format = options.format || 'json';

      const report = {
        id: reportId,
        templateId,
        template,
        startDate,
        endDate,
        format,
        status: 'generating',
        createdAt: new Date(),
        createdBy: options.userId,
        options
      };

      this.reports.set(reportId, report);

      // Generate report data
      const reportData = await this.collectReportData(template, startDate, endDate, options);
      
      // Process and format data
      const processedData = await this.processReportData(reportData, template, options);
      
      // Generate output file
      const outputPath = await this.generateReportFile(report, processedData);

      // Update report status
      report.status = 'completed';
      report.completedAt = new Date();
      report.outputPath = outputPath;
      report.data = processedData;

      this.reports.set(reportId, report);

      await this.auditService.log(
        options.userId,
        'report_generated',
        { reportId, templateId, format, outputPath },
        'info'
      );

      logger.info(`Report generated: ${reportId} (${templateId})`);
      return report;
    } catch (error) {
      logger.error('Error generating report:', error);
      throw error;
    }
  }

  async collectReportData(template, startDate, endDate, options) {
    const data = {};

    switch (template.dataSource) {
      case 'users':
        data.users = await this.getUserAnalytics(startDate, endDate);
        break;
      case 'monitoring':
        data.monitoring = await this.getSystemMetrics(startDate, endDate);
        break;
      case 'billing':
        data.billing = await this.getBillingAnalytics(startDate, endDate);
        break;
      case 'security':
        data.security = await this.getSecurityMetrics(startDate, endDate);
        break;
      case 'support':
        data.support = await this.getSupportAnalytics(startDate, endDate);
        break;
      default:
        throw new Error(`Unknown data source: ${template.dataSource}`);
    }

    return data;
  }

  async getUserAnalytics(startDate, endDate) {
    // Mock user analytics data - would integrate with actual user service
    return {
      totalUsers: 1250,
      activeUsers: 890,
      newRegistrations: 45,
      userRetention: 78.5,
      loginFrequency: {
        daily: 320,
        weekly: 560,
        monthly: 890
      },
      featureUsage: {
        dashboard: 95,
        billing: 67,
        support: 34,
        api: 23
      },
      geographicDistribution: {
        'North America': 45,
        'Europe': 32,
        'Asia': 18,
        'Other': 5
      },
      dailyActiveUsers: this.generateTimeSeriesData(startDate, endDate, 200, 400),
      registrationTrend: this.generateTimeSeriesData(startDate, endDate, 5, 15)
    };
  }

  async getSystemMetrics(startDate, endDate) {
    // Mock system metrics - would integrate with monitoring service
    return {
      averageCpuUsage: 65.2,
      averageMemoryUsage: 72.8,
      averageDiskUsage: 45.3,
      networkTraffic: {
        inbound: 1250000000, // bytes
        outbound: 890000000
      },
      averageResponseTime: 245, // ms
      errorRate: 0.02, // 2%
      uptime: 99.95,
      cpuTrend: this.generateTimeSeriesData(startDate, endDate, 50, 80),
      memoryTrend: this.generateTimeSeriesData(startDate, endDate, 60, 85),
      responseTimes: this.generateTimeSeriesData(startDate, endDate, 100, 500)
    };
  }

  async getBillingAnalytics(startDate, endDate) {
    // Mock billing analytics - would integrate with billing service
    return {
      totalRevenue: 125000,
      monthlyRecurringRevenue: 45000,
      customerLifetimeValue: 2400,
      churnRate: 3.2,
      conversionRate: 12.5,
      paymentSuccessRate: 97.8,
      revenueTrend: this.generateTimeSeriesData(startDate, endDate, 3000, 6000),
      subscriptionGrowth: this.generateTimeSeriesData(startDate, endDate, 10, 25),
      planDistribution: {
        basic: 45,
        professional: 35,
        enterprise: 20
      }
    };
  }

  async getSecurityMetrics(startDate, endDate) {
    // Mock security metrics - would integrate with security service
    return {
      securityIncidents: 3,
      failedLogins: 127,
      blockedIps: 45,
      vulnerabilityScans: 12,
      complianceScore: 94.5,
      threatLevel: 'low',
      incidentTrend: this.generateTimeSeriesData(startDate, endDate, 0, 2),
      failedLoginTrend: this.generateTimeSeriesData(startDate, endDate, 5, 15),
      vulnerabilityTypes: {
        'SQL Injection': 2,
        'XSS': 1,
        'CSRF': 0,
        'Authentication': 3
      }
    };
  }

  async getSupportAnalytics(startDate, endDate) {
    // Mock support analytics - would integrate with support service
    return {
      ticketVolume: 234,
      averageResolutionTime: 4.2, // hours
      customerSatisfaction: 4.6, // out of 5
      firstContactResolution: 78.5,
      escalationRate: 12.3,
      ticketTrend: this.generateTimeSeriesData(startDate, endDate, 5, 15),
      resolutionTimeTrend: this.generateTimeSeriesData(startDate, endDate, 2, 8),
      categoryDistribution: {
        'Technical Support': 45,
        'Billing': 25,
        'Feature Request': 20,
        'Bug Report': 10
      },
      agentPerformance: [
        { name: 'Agent A', tickets: 45, avgTime: 3.2, satisfaction: 4.8 },
        { name: 'Agent B', tickets: 38, avgTime: 4.1, satisfaction: 4.5 },
        { name: 'Agent C', tickets: 52, avgTime: 3.8, satisfaction: 4.7 }
      ]
    };
  }

  async processReportData(rawData, template, options) {
    const processedData = {
      metadata: {
        reportId: options.reportId,
        template: template.name,
        generatedAt: new Date(),
        period: {
          start: options.startDate,
          end: options.endDate
        },
        metrics: template.metrics
      },
      summary: {},
      details: rawData,
      charts: []
    };

    // Generate summary statistics
    for (const [dataSource, data] of Object.entries(rawData)) {
      processedData.summary[dataSource] = this.generateSummary(data, template.metrics);
    }

    // Generate chart configurations
    processedData.charts = this.generateChartConfigs(rawData, template);

    return processedData;
  }

  generateSummary(data, metrics) {
    const summary = {};
    
    // Extract key metrics based on template configuration
    for (const metric of metrics) {
      if (data[metric] !== undefined) {
        summary[metric] = data[metric];
      }
    }

    return summary;
  }

  generateChartConfigs(data, template) {
    const charts = [];

    // Generate chart configurations based on template
    for (const chartType of template.charts) {
      switch (chartType) {
        case 'line':
          charts.push(this.generateLineChart(data, template));
          break;
        case 'bar':
          charts.push(this.generateBarChart(data, template));
          break;
        case 'pie':
          charts.push(this.generatePieChart(data, template));
          break;
        case 'area':
          charts.push(this.generateAreaChart(data, template));
          break;
      }
    }

    return charts;
  }

  generateLineChart(data, template) {
    // Generate line chart configuration
    return {
      type: 'line',
      title: `${template.name} - Trend Analysis`,
      data: {
        labels: this.generateDateLabels(30),
        datasets: this.extractTimeSeriesData(data)
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true }
        }
      }
    };
  }

  generateBarChart(data, template) {
    return {
      type: 'bar',
      title: `${template.name} - Comparison`,
      data: {
        labels: Object.keys(data[Object.keys(data)[0]] || {}),
        datasets: [{
          label: template.name,
          data: Object.values(data[Object.keys(data)[0]] || {})
        }]
      }
    };
  }

  generatePieChart(data, template) {
    return {
      type: 'pie',
      title: `${template.name} - Distribution`,
      data: {
        labels: Object.keys(data[Object.keys(data)[0]] || {}),
        datasets: [{
          data: Object.values(data[Object.keys(data)[0]] || {})
        }]
      }
    };
  }

  generateAreaChart(data, template) {
    return {
      type: 'area',
      title: `${template.name} - Area Analysis`,
      data: {
        labels: this.generateDateLabels(30),
        datasets: this.extractTimeSeriesData(data)
      },
      options: {
        fill: true,
        tension: 0.4
      }
    };
  }

  async generateReportFile(report, data) {
    const filename = `${report.templateId}_${report.id}.${report.format}`;
    const outputPath = path.join(this.config.outputDir, filename);

    switch (report.format) {
      case 'json':
        fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));
        break;
      case 'csv':
        const csv = this.convertToCSV(data);
        fs.writeFileSync(outputPath, csv);
        break;
      case 'pdf':
        // Would generate PDF using a library like puppeteer or jsPDF
        fs.writeFileSync(outputPath, JSON.stringify(data, null, 2)); // Placeholder
        break;
      default:
        throw new Error(`Unsupported format: ${report.format}`);
    }

    return outputPath;
  }

  convertToCSV(data) {
    // Simple CSV conversion - would be more sophisticated in production
    const rows = [];
    
    // Add headers
    rows.push('Metric,Value');
    
    // Add summary data
    if (data.summary) {
      for (const [category, metrics] of Object.entries(data.summary)) {
        for (const [metric, value] of Object.entries(metrics)) {
          rows.push(`${category}_${metric},${value}`);
        }
      }
    }

    return rows.join('\n');
  }

  generateTimeSeriesData(startDate, endDate, min, max) {
    const data = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      data.push({
        date: new Date(current),
        value: Math.floor(Math.random() * (max - min + 1)) + min
      });
      current.setDate(current.getDate() + 1);
    }

    return data;
  }

  generateDateLabels(days) {
    const labels = [];
    const current = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(current);
      date.setDate(date.getDate() - i);
      labels.push(date.toISOString().split('T')[0]);
    }

    return labels;
  }

  extractTimeSeriesData(data) {
    const datasets = [];
    
    for (const [key, value] of Object.entries(data)) {
      if (Array.isArray(value) && value.length > 0 && value[0].date) {
        datasets.push({
          label: key,
          data: value.map(item => item.value)
        });
      }
    }

    return datasets;
  }

  getDefaultStartDate(days) {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date;
  }

  generateReportId() {
    return 'RPT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
  }

  async scheduleReport(templateId, schedule, options = {}) {
    const scheduleId = this.generateReportId();
    
    const scheduledReport = {
      id: scheduleId,
      templateId,
      schedule,
      options,
      nextRun: this.calculateNextRun(schedule),
      createdAt: new Date(),
      createdBy: options.userId,
      enabled: true
    };

    this.scheduledReports.set(scheduleId, scheduledReport);

    await this.auditService.log(
      options.userId,
      'report_scheduled',
      { scheduleId, templateId, schedule },
      'info'
    );

    return scheduledReport;
  }

  calculateNextRun(schedule) {
    const now = new Date();
    
    switch (schedule.frequency) {
      case 'daily':
        now.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        now.setDate(now.getDate() + 7);
        break;
      case 'monthly':
        now.setMonth(now.getMonth() + 1);
        break;
      default:
        now.setDate(now.getDate() + 1);
    }

    return now;
  }

  getAvailableTemplates() {
    return Array.from(this.templates.entries()).map(([id, template]) => ({
      id,
      ...template
    }));
  }

  getReportHistory(limit = 50) {
    return Array.from(this.reports.values())
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, limit);
  }

  async getReportAnalytics() {
    const reports = Array.from(this.reports.values());
    
    return {
      totalReports: reports.length,
      reportsThisMonth: reports.filter(r => 
        r.createdAt >= new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      ).length,
      popularTemplates: this.getMostUsedTemplates(reports),
      averageGenerationTime: this.getAverageGenerationTime(reports),
      formatDistribution: this.getFormatDistribution(reports)
    };
  }

  getMostUsedTemplates(reports) {
    const usage = {};
    reports.forEach(report => {
      usage[report.templateId] = (usage[report.templateId] || 0) + 1;
    });

    return Object.entries(usage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([templateId, count]) => ({ templateId, count }));
  }

  getAverageGenerationTime(reports) {
    const completedReports = reports.filter(r => r.completedAt && r.createdAt);
    if (completedReports.length === 0) return 0;

    const totalTime = completedReports.reduce((sum, report) => 
      sum + (report.completedAt - report.createdAt), 0
    );

    return Math.round(totalTime / completedReports.length / 1000); // seconds
  }

  getFormatDistribution(reports) {
    const distribution = {};
    reports.forEach(report => {
      distribution[report.format] = (distribution[report.format] || 0) + 1;
    });

    return distribution;
  }
}
