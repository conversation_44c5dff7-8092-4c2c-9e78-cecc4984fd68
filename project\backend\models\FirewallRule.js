import mongoose from 'mongoose';

const firewallRuleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  ruleType: {
    type: String,
    required: true,
    enum: ['allow', 'deny', 'drop', 'reject', 'log'],
    default: 'allow'
  },
  priority: {
    type: Number,
    required: true,
    min: 1,
    max: 1000,
    default: 100
  },
  isActive: {
    type: Boolean,
    default: true
  },
  direction: {
    type: String,
    required: true,
    enum: ['inbound', 'outbound', 'both'],
    default: 'inbound'
  },
  protocol: {
    type: String,
    required: true,
    enum: ['tcp', 'udp', 'icmp', 'all', 'esp', 'ah', 'gre'],
    default: 'tcp'
  },
  source: {
    ipAddress: {
      type: String,
      validate: {
        validator: function(v) {
          if (!v || v === 'any') return true;
          // Validate IPv4/IPv6 with CIDR notation
          const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/;
          const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(?:\/(?:[0-9]|[1-9][0-9]|1[0-1][0-9]|12[0-8]))?$/;
          return ipv4Regex.test(v) || ipv6Regex.test(v);
        },
        message: 'Invalid source IP address format'
      },
      default: 'any'
    },
    portRange: {
      start: {
        type: Number,
        min: 1,
        max: 65535
      },
      end: {
        type: Number,
        min: 1,
        max: 65535
      }
    },
    ports: [{
      type: Number,
      min: 1,
      max: 65535
    }],
    zone: {
      type: String,
      enum: ['internal', 'external', 'dmz', 'trusted', 'untrusted', 'any'],
      default: 'any'
    }
  },
  destination: {
    ipAddress: {
      type: String,
      validate: {
        validator: function(v) {
          if (!v || v === 'any') return true;
          const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/;
          const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(?:\/(?:[0-9]|[1-9][0-9]|1[0-1][0-9]|12[0-8]))?$/;
          return ipv4Regex.test(v) || ipv6Regex.test(v);
        },
        message: 'Invalid destination IP address format'
      },
      default: 'any'
    },
    portRange: {
      start: {
        type: Number,
        min: 1,
        max: 65535
      },
      end: {
        type: Number,
        min: 1,
        max: 65535
      }
    },
    ports: [{
      type: Number,
      min: 1,
      max: 65535
    }],
    zone: {
      type: String,
      enum: ['internal', 'external', 'dmz', 'trusted', 'untrusted', 'any'],
      default: 'any'
    }
  },
  conditions: {
    timeRestriction: {
      enabled: {
        type: Boolean,
        default: false
      },
      schedule: {
        days: [{
          type: String,
          enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        }],
        timeRange: {
          start: String, // Format: "HH:MM"
          end: String    // Format: "HH:MM"
        },
        timezone: {
          type: String,
          default: 'UTC'
        }
      }
    },
    rateLimiting: {
      enabled: {
        type: Boolean,
        default: false
      },
      maxConnections: Number,
      maxConnectionsPerIP: Number,
      timeWindow: {
        type: Number,
        default: 60 // seconds
      }
    },
    geoLocation: {
      allowedCountries: [String],
      blockedCountries: [String]
    },
    contentInspection: {
      enabled: {
        type: Boolean,
        default: false
      },
      patterns: [String],
      action: {
        type: String,
        enum: ['block', 'log', 'alert'],
        default: 'block'
      }
    }
  },
  logging: {
    enabled: {
      type: Boolean,
      default: true
    },
    level: {
      type: String,
      enum: ['none', 'basic', 'detailed', 'verbose'],
      default: 'basic'
    },
    logMatches: {
      type: Boolean,
      default: true
    },
    logDrops: {
      type: Boolean,
      default: true
    }
  },
  statistics: {
    matchCount: {
      type: Number,
      default: 0
    },
    lastMatch: Date,
    bytesTransferred: {
      type: Number,
      default: 0
    },
    connectionsBlocked: {
      type: Number,
      default: 0
    },
    connectionsAllowed: {
      type: Number,
      default: 0
    }
  },
  metadata: {
    category: {
      type: String,
      enum: ['security', 'performance', 'compliance', 'custom'],
      default: 'security'
    },
    tags: [String],
    environment: {
      type: String,
      enum: ['production', 'staging', 'development', 'testing'],
      default: 'production'
    },
    compliance: {
      standards: [{
        type: String,
        enum: ['PCI-DSS', 'HIPAA', 'SOX', 'GDPR', 'ISO27001']
      }],
      required: {
        type: Boolean,
        default: false
      }
    }
  },
  deployment: {
    status: {
      type: String,
      enum: ['draft', 'pending', 'deployed', 'failed', 'rollback'],
      default: 'draft'
    },
    deployedAt: Date,
    deployedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rollbackVersion: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'FirewallRule'
    },
    deploymentErrors: [String]
  },
  expirationDate: Date,
  autoExpire: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: Date
}, {
  timestamps: true
});

// Indexes
firewallRuleSchema.index({ priority: 1, isActive: 1 });
firewallRuleSchema.index({ ruleType: 1, isActive: 1 });
firewallRuleSchema.index({ 'source.ipAddress': 1 });
firewallRuleSchema.index({ 'destination.ipAddress': 1 });
firewallRuleSchema.index({ protocol: 1, direction: 1 });
firewallRuleSchema.index({ 'deployment.status': 1 });
firewallRuleSchema.index({ 'metadata.category': 1 });
firewallRuleSchema.index({ 'metadata.tags': 1 });
firewallRuleSchema.index({ expirationDate: 1 });
firewallRuleSchema.index({ createdBy: 1 });

// Virtual for checking if rule is expired
firewallRuleSchema.virtual('isExpired').get(function() {
  return this.expirationDate && this.expirationDate < new Date();
});

// Methods
firewallRuleSchema.methods.isActiveAtTime = function(timestamp = new Date()) {
  if (!this.conditions.timeRestriction.enabled) return true;
  
  const day = timestamp.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const time = timestamp.toTimeString().slice(0, 5); // HH:MM format
  
  const schedule = this.conditions.timeRestriction.schedule;
  
  // Check if current day is allowed
  if (schedule.days.length > 0 && !schedule.days.includes(day)) {
    return false;
  }
  
  // Check if current time is within allowed range
  if (schedule.timeRange.start && schedule.timeRange.end) {
    return time >= schedule.timeRange.start && time <= schedule.timeRange.end;
  }
  
  return true;
};

firewallRuleSchema.methods.matchesTraffic = function(traffic) {
  // Check protocol
  if (this.protocol !== 'all' && this.protocol !== traffic.protocol) {
    return false;
  }
  
  // Check direction
  if (this.direction !== 'both' && this.direction !== traffic.direction) {
    return false;
  }
  
  // Check source IP
  if (this.source.ipAddress !== 'any' && !this.ipMatches(this.source.ipAddress, traffic.sourceIP)) {
    return false;
  }
  
  // Check destination IP
  if (this.destination.ipAddress !== 'any' && !this.ipMatches(this.destination.ipAddress, traffic.destinationIP)) {
    return false;
  }
  
  // Check ports
  if (!this.portMatches(this.destination.ports, this.destination.portRange, traffic.destinationPort)) {
    return false;
  }
  
  return true;
};

firewallRuleSchema.methods.ipMatches = function(ruleIP, trafficIP) {
  if (ruleIP === 'any') return true;
  
  // Simple IP matching (would need more sophisticated CIDR matching in production)
  if (ruleIP.includes('/')) {
    // CIDR notation - simplified check
    const [network] = ruleIP.split('/');
    return trafficIP.startsWith(network.split('.').slice(0, 3).join('.'));
  }
  
  return ruleIP === trafficIP;
};

firewallRuleSchema.methods.portMatches = function(rulePorts, rulePortRange, trafficPort) {
  // Check specific ports
  if (rulePorts && rulePorts.length > 0) {
    return rulePorts.includes(trafficPort);
  }
  
  // Check port range
  if (rulePortRange && rulePortRange.start && rulePortRange.end) {
    return trafficPort >= rulePortRange.start && trafficPort <= rulePortRange.end;
  }
  
  return true; // No port restrictions
};

firewallRuleSchema.methods.updateStatistics = function(action, bytes = 0) {
  this.statistics.matchCount += 1;
  this.statistics.lastMatch = new Date();
  this.statistics.bytesTransferred += bytes;
  
  if (action === 'allow') {
    this.statistics.connectionsAllowed += 1;
  } else {
    this.statistics.connectionsBlocked += 1;
  }
  
  return this.save();
};

// Static methods
firewallRuleSchema.statics.getActiveRules = function(direction = null) {
  const query = { 
    isActive: true,
    $or: [
      { expirationDate: { $exists: false } },
      { expirationDate: { $gt: new Date() } }
    ]
  };
  
  if (direction) {
    query.$or = [
      { direction: direction },
      { direction: 'both' }
    ];
  }
  
  return this.find(query).sort({ priority: 1 });
};

firewallRuleSchema.statics.findMatchingRules = function(traffic) {
  return this.getActiveRules(traffic.direction).then(rules => {
    return rules.filter(rule => rule.matchesTraffic(traffic));
  });
};

firewallRuleSchema.statics.cleanupExpired = function() {
  return this.updateMany(
    { 
      expirationDate: { $lt: new Date() },
      autoExpire: true
    },
    { isActive: false }
  );
};

// Pre-save middleware
firewallRuleSchema.pre('save', function(next) {
  // Validate port ranges
  if (this.source.portRange && this.source.portRange.start > this.source.portRange.end) {
    return next(new Error('Source port range start cannot be greater than end'));
  }
  
  if (this.destination.portRange && this.destination.portRange.start > this.destination.portRange.end) {
    return next(new Error('Destination port range start cannot be greater than end'));
  }
  
  next();
});

export const FirewallRule = mongoose.model('FirewallRule', firewallRuleSchema);
