import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import { adminMiddleware, requireAdminPermission } from '../middleware/adminMiddleware.js';
import {
  // User Management
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  suspendUser,
  updateUserRole,
  enforceGlobalMFA,
  impersonate<PERSON><PERSON>,
  getUserActivity,
  getUserLoginHistory,

  // Developer & Reseller Management
  getResellerRequests,
  approveResellerRequest,
  setResourceLimits,
  getResellerActivity,
  updateWhiteLabeling,

  // Server & Infrastructure Management
  getHostingNodes,
  addHostingNode,
  configureWebServer,
  manageLoadBalancer,
  getContainerClusters,

  // Security & Compliance
  updateSecurityPolicy,
  getSecurityAuditLogs,
  manageIPWhitelist,
  configureFirewallRules,
  initiateSecurityScan,
  getComplianceReports,

  // Billing & Payment Management
  updateSubscriptionPlan,
  getRevenueAnalytics,
  manageCoupons,
  processRefunds,
  getBillingHistory,

  // Platform Configuration
  updateBrandingSettings,
  configureGlobalSettings,
  manageEmailTemplates,
  updateFeatureAvailability,

  // AI/ML Features Management
  manageAIFeatures,
  updateAIModels,
  setAIThresholds,
  getAIUsageReports,

  // Monitoring & Resource Management
  getResourceMetrics,
  configureAlerts,
  getOptimizationRecommendations,
  manualResourceAllocation,

  // Backup & Disaster Recovery
  initiateGlobalBackup,
  getBackupStatus,
  configureBackupSettings,
  restoreFromBackup,

  // Analytics & Reporting
  getPlatformReports,
  scheduleReports,
  getUserGrowthMetrics,
  getFeatureEngagement,

  // Support & Ticketing
  getAllTickets,
  assignTicket,
  manageKnowledgeBase,
  collectFeedback,

  // API Management
  manageAPIKeys,
  getAPIUsage,
  configureWebhooks,

  // Migration & Import
  initiateMigration,
  getMigrationStatus,

  // Notifications & Announcements
  createAnnouncement,
  configureNotifications,

  // Version Control & Updates
  triggerUpdates,
  manageVersions,

  // Multi-Tenancy
  manageTenants,
  getTenantReports,

  // Audit & Logs
  getAuditLogs,
  exportLogs,

  // Legal & Compliance
  updateLegalDocuments,
  handleDataRequests
} from '../controllers/adminController.js';

const router = express.Router();

// User Management Routes
router.get('/users', authMiddleware, adminMiddleware, getUsers);
router.post('/users', authMiddleware, requireAdminPermission('users:create'), createUser);
router.put('/users/:id', authMiddleware, requireAdminPermission('users:update'), updateUser);
router.delete('/users/:id', authMiddleware, requireAdminPermission('users:delete'), deleteUser);
router.post('/users/:id/suspend', authMiddleware, requireAdminPermission('users:suspend'), suspendUser);
router.post('/users/:id/role', authMiddleware, adminMiddleware, updateUserRole);
router.post('/users/mfa', authMiddleware, adminMiddleware, enforceGlobalMFA);
router.post('/users/:id/impersonate', authMiddleware, requireAdminPermission('users:impersonate'), impersonateUser);
router.get('/users/:id/activity', authMiddleware, adminMiddleware, getUserActivity);
router.get('/users/:id/login-history', authMiddleware, adminMiddleware, getUserLoginHistory);

// Developer & Reseller Management Routes
router.get('/resellers/requests', authMiddleware, adminMiddleware, getResellerRequests);
router.post('/resellers/:id/approve', authMiddleware, adminMiddleware, approveResellerRequest);
router.post('/resellers/:id/limits', authMiddleware, adminMiddleware, setResourceLimits);
router.get('/resellers/:id/activity', authMiddleware, adminMiddleware, getResellerActivity);
router.post('/resellers/:id/branding', authMiddleware, adminMiddleware, updateWhiteLabeling);

// Server & Infrastructure Management Routes
router.get('/infrastructure/nodes', authMiddleware, adminMiddleware, getHostingNodes);
router.post('/infrastructure/nodes', authMiddleware, requireAdminPermission('infrastructure:manage'), addHostingNode);
router.post('/infrastructure/webserver', authMiddleware, adminMiddleware, configureWebServer);
router.post('/infrastructure/loadbalancer', authMiddleware, adminMiddleware, manageLoadBalancer);
router.get('/infrastructure/containers', authMiddleware, adminMiddleware, getContainerClusters);

// Security & Compliance Routes
router.post('/security/policy', authMiddleware, adminMiddleware, updateSecurityPolicy);
router.get('/security/audit-logs', authMiddleware, adminMiddleware, getSecurityAuditLogs);
router.post('/security/ip-whitelist', authMiddleware, adminMiddleware, manageIPWhitelist);
router.post('/security/firewall', authMiddleware, adminMiddleware, configureFirewallRules);
router.post('/security/scan', authMiddleware, adminMiddleware, initiateSecurityScan);
router.get('/security/compliance', authMiddleware, adminMiddleware, getComplianceReports);

// Billing & Payment Management Routes
router.post('/billing/plans', authMiddleware, adminMiddleware, updateSubscriptionPlan);
router.get('/billing/analytics', authMiddleware, adminMiddleware, getRevenueAnalytics);
router.post('/billing/coupons', authMiddleware, adminMiddleware, manageCoupons);
router.post('/billing/refunds', authMiddleware, adminMiddleware, processRefunds);
router.get('/billing/history', authMiddleware, adminMiddleware, getBillingHistory);

// Platform Configuration Routes
router.post('/platform/branding', authMiddleware, adminMiddleware, updateBrandingSettings);
router.post('/platform/settings', authMiddleware, adminMiddleware, configureGlobalSettings);
router.post('/platform/email-templates', authMiddleware, adminMiddleware, manageEmailTemplates);
router.post('/platform/features', authMiddleware, adminMiddleware, updateFeatureAvailability);

// AI/ML Features Management Routes
router.post('/ai/features', authMiddleware, adminMiddleware, manageAIFeatures);
router.post('/ai/models', authMiddleware, adminMiddleware, updateAIModels);
router.post('/ai/thresholds', authMiddleware, adminMiddleware, setAIThresholds);
router.get('/ai/usage', authMiddleware, adminMiddleware, getAIUsageReports);

// Monitoring & Resource Management Routes
router.get('/resources/metrics', authMiddleware, adminMiddleware, getResourceMetrics);
router.post('/resources/alerts', authMiddleware, adminMiddleware, configureAlerts);
router.get('/resources/recommendations', authMiddleware, adminMiddleware, getOptimizationRecommendations);
router.post('/resources/allocate', authMiddleware, adminMiddleware, manualResourceAllocation);

// Backup & Disaster Recovery Routes
router.post('/backup/global', authMiddleware, adminMiddleware, initiateGlobalBackup);
router.get('/backup/status', authMiddleware, adminMiddleware, getBackupStatus);
router.post('/backup/settings', authMiddleware, adminMiddleware, configureBackupSettings);
router.post('/backup/restore', authMiddleware, adminMiddleware, restoreFromBackup);

// Analytics & Reporting Routes
router.get('/reports/platform', authMiddleware, adminMiddleware, getPlatformReports);
router.post('/reports/schedule', authMiddleware, adminMiddleware, scheduleReports);
router.get('/reports/user-growth', authMiddleware, adminMiddleware, getUserGrowthMetrics);
router.get('/reports/feature-engagement', authMiddleware, adminMiddleware, getFeatureEngagement);

// Support & Ticketing Routes
router.get('/support/tickets', authMiddleware, adminMiddleware, getAllTickets);
router.post('/support/tickets/:id/assign', authMiddleware, adminMiddleware, assignTicket);
router.post('/support/knowledge-base', authMiddleware, adminMiddleware, manageKnowledgeBase);
router.post('/support/feedback', authMiddleware, adminMiddleware, collectFeedback);

// API Management Routes
router.post('/api/keys', authMiddleware, adminMiddleware, manageAPIKeys);
router.get('/api/usage', authMiddleware, adminMiddleware, getAPIUsage);
router.post('/api/webhooks', authMiddleware, adminMiddleware, configureWebhooks);

// Migration & Import Routes
router.post('/migration/initiate', authMiddleware, adminMiddleware, initiateMigration);
router.get('/migration/status', authMiddleware, adminMiddleware, getMigrationStatus);

// Notifications & Announcements Routes
router.post('/announcements', authMiddleware, adminMiddleware, createAnnouncement);
router.post('/notifications/config', authMiddleware, adminMiddleware, configureNotifications);

// Version Control & Updates Routes
router.post('/updates/trigger', authMiddleware, adminMiddleware, triggerUpdates);
router.post('/versions/manage', authMiddleware, adminMiddleware, manageVersions);

// Multi-Tenancy Routes
router.post('/tenants', authMiddleware, adminMiddleware, manageTenants);
router.get('/tenants/reports', authMiddleware, adminMiddleware, getTenantReports);

// Audit & Logs Routes
router.get('/audit/logs', authMiddleware, adminMiddleware, getAuditLogs);
router.post('/audit/export', authMiddleware, adminMiddleware, exportLogs);

// Legal & Compliance Routes
router.post('/legal/documents', authMiddleware, adminMiddleware, updateLegalDocuments);
router.post('/legal/data-requests', authMiddleware, adminMiddleware, handleDataRequests);

export default router;