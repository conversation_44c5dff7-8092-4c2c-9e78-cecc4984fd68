import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import { requireRole } from '../middleware/roleMiddleware.js';
import {
  // Dashboard
  getClientDashboard,
  
  // Profile Management
  getClientProfile,
  updateClientProfile,
  
  // Website Management
  getWebsites,
  addWebsite,
  updateWebsite,
  deleteWebsite,
  getWebsiteDetails,
  toggleWebsiteStatus,
  clearWebsiteCache,
  
  // Analytics & Reporting
  getWebsiteAnalytics,
  getTrafficReport,
  getPerformanceReport,
  getSecurityReport,
  downloadReport,
  
  // File & Media Management
  getFileManager,
  uploadFile,
  downloadFile,
  deleteFile,
  createFolder,
  
  // Form Builder & Lead Management
  getForms,
  createForm,
  updateForm,
  deleteForm,
  getFormSubmissions,
  exportLeads,
  
  // Email Management
  getEmailAccounts,
  createEmailAccount,
  updateEmailAccount,
  deleteEmailAccount,
  getWebmailAccess,
  updateEmailSettings,
  
  // Security & Privacy
  getSecuritySettings,
  updateSecuritySettings,
  getSecurityScore,
  requestSecurityScan,
  getPrivacySettings,
  updatePrivacySettings,
  
  // Domain & DNS
  getDomains,
  getDomainDetails,
  requestDomainChange,
  getDNSRecords,
  
  // Backup & Restore
  getBackups,
  requestBackup,
  requestRestore,
  downloadBackup,
  
  // Billing & Subscription
  getBillingInfo,
  getInvoices,
  downloadInvoice,
  updatePaymentMethod,
  updateSubscription,
  applyPromoCode,
  
  // Task & Request Center
  getTasks,
  createTask,
  updateTask,
  getTaskDetails,
  approveTask,
  
  // Support & Helpdesk
  getSupportTickets,
  createSupportTicket,
  updateSupportTicket,
  getSupportTicketDetails,
  getKnowledgeBase,
  
  // Team Management
  getTeamMembers,
  inviteTeamMember,
  updateTeamMember,
  removeTeamMember,
  
  // Notifications & Alerts
  getNotifications,
  markNotificationRead,
  updateNotificationSettings,
  getAlerts,
  
  // Marketplace
  getMarketplace,
  purchaseAddon,
  getInstalledAddons,
  
  // Integrations
  getIntegrations,
  connectIntegration,
  disconnectIntegration,
  updateIntegrationSettings
} from '../controllers/clientController.js';

const router = express.Router();

// Apply authentication and client role middleware to all routes
router.use(authMiddleware);
router.use(requireRole('client'));

// ===== DASHBOARD =====
router.get('/dashboard', getClientDashboard);

// ===== PROFILE MANAGEMENT =====
router.get('/profile', getClientProfile);
router.put('/profile', updateClientProfile);

// ===== WEBSITE MANAGEMENT =====
router.get('/websites', getWebsites);
router.post('/websites', addWebsite);
router.get('/websites/:websiteId', getWebsiteDetails);
router.put('/websites/:websiteId', updateWebsite);
router.delete('/websites/:websiteId', deleteWebsite);
router.post('/websites/:websiteId/toggle-status', toggleWebsiteStatus);
router.post('/websites/:websiteId/clear-cache', clearWebsiteCache);

// ===== ANALYTICS & REPORTING =====
router.get('/websites/:websiteId/analytics', getWebsiteAnalytics);
router.get('/reports/traffic', getTrafficReport);
router.get('/reports/performance', getPerformanceReport);
router.get('/reports/security', getSecurityReport);
router.get('/reports/:reportId/download', downloadReport);

// ===== FILE & MEDIA MANAGEMENT =====
router.get('/websites/:websiteId/files', getFileManager);
router.post('/websites/:websiteId/files/upload', uploadFile);
router.get('/websites/:websiteId/files/download/*', downloadFile);
router.delete('/websites/:websiteId/files/*', deleteFile);
router.post('/websites/:websiteId/files/folder', createFolder);

// ===== FORM BUILDER & LEAD MANAGEMENT =====
router.get('/forms', getForms);
router.post('/forms', createForm);
router.put('/forms/:formId', updateForm);
router.delete('/forms/:formId', deleteForm);
router.get('/forms/:formId/submissions', getFormSubmissions);
router.get('/leads/export', exportLeads);

// ===== EMAIL MANAGEMENT =====
router.get('/email/accounts', getEmailAccounts);
router.post('/email/accounts', createEmailAccount);
router.put('/email/accounts/:emailId', updateEmailAccount);
router.delete('/email/accounts/:emailId', deleteEmailAccount);
router.get('/email/webmail', getWebmailAccess);
router.put('/email/settings', updateEmailSettings);

// ===== SECURITY & PRIVACY =====
router.get('/security', getSecuritySettings);
router.put('/security', updateSecuritySettings);
router.get('/security/score', getSecurityScore);
router.post('/security/scan', requestSecurityScan);
router.get('/privacy', getPrivacySettings);
router.put('/privacy', updatePrivacySettings);

// ===== DOMAIN & DNS =====
router.get('/domains', getDomains);
router.get('/domains/:domainId', getDomainDetails);
router.post('/domains/:domainId/change-request', requestDomainChange);
router.get('/domains/:domainId/dns', getDNSRecords);

// ===== BACKUP & RESTORE =====
router.get('/backups', getBackups);
router.post('/backups', requestBackup);
router.post('/backups/:backupId/restore', requestRestore);
router.get('/backups/:backupId/download', downloadBackup);

// ===== BILLING & SUBSCRIPTION =====
router.get('/billing', getBillingInfo);
router.get('/billing/invoices', getInvoices);
router.get('/billing/invoices/:invoiceId/download', downloadInvoice);
router.put('/billing/payment-method', updatePaymentMethod);
router.put('/billing/subscription', updateSubscription);
router.post('/billing/promo-code', applyPromoCode);

// ===== TASK & REQUEST CENTER =====
router.get('/tasks', getTasks);
router.post('/tasks', createTask);
router.put('/tasks/:taskId', updateTask);
router.get('/tasks/:taskId', getTaskDetails);
router.post('/tasks/:taskId/approve', approveTask);

// ===== SUPPORT & HELPDESK =====
router.get('/support/tickets', getSupportTickets);
router.post('/support/tickets', createSupportTicket);
router.put('/support/tickets/:ticketId', updateSupportTicket);
router.get('/support/tickets/:ticketId', getSupportTicketDetails);
router.get('/support/knowledge-base', getKnowledgeBase);

// ===== TEAM MANAGEMENT =====
router.get('/team', getTeamMembers);
router.post('/team/invite', inviteTeamMember);
router.put('/team/:memberId', updateTeamMember);
router.delete('/team/:memberId', removeTeamMember);

// ===== NOTIFICATIONS & ALERTS =====
router.get('/notifications', getNotifications);
router.put('/notifications/:notificationId/read', markNotificationRead);
router.put('/notifications/settings', updateNotificationSettings);
router.get('/alerts', getAlerts);

// ===== MARKETPLACE =====
router.get('/marketplace', getMarketplace);
router.post('/marketplace/:addonId/purchase', purchaseAddon);
router.get('/marketplace/installed', getInstalledAddons);

// ===== INTEGRATIONS =====
router.get('/integrations', getIntegrations);
router.post('/integrations/:provider/connect', connectIntegration);
router.delete('/integrations/:integrationId', disconnectIntegration);
router.put('/integrations/:integrationId/settings', updateIntegrationSettings);

// ===== QUICK ACTIONS =====
router.get('/quick-stats', async (req, res) => {
  try {
    // Mock quick stats for dashboard widgets
    res.json({
      websites: {
        total: 3,
        online: 3,
        offline: 0
      },
      traffic: {
        today: 1250,
        yesterday: 1180,
        change: 5.9
      },
      storage: {
        used: 2.5, // GB
        total: 10,
        percentage: 25
      },
      bandwidth: {
        used: 15.2, // GB
        total: 100,
        percentage: 15.2
      },
      uptime: {
        percentage: 99.9,
        status: 'excellent'
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/recent-activity', async (req, res) => {
  try {
    // Mock recent activity
    res.json({
      activities: [
        {
          type: 'website_updated',
          message: 'Website "My Portfolio" was updated',
          timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          icon: 'globe'
        },
        {
          type: 'backup_completed',
          message: 'Automatic backup completed successfully',
          timestamp: new Date(Date.now() - 7200000), // 2 hours ago
          icon: 'shield'
        },
        {
          type: 'ssl_renewed',
          message: 'SSL certificate renewed for example.com',
          timestamp: new Date(Date.now() - 86400000), // 1 day ago
          icon: 'lock'
        }
      ]
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/system-status', async (req, res) => {
  try {
    // Mock system status
    res.json({
      overall: 'operational',
      services: [
        { name: 'Web Hosting', status: 'operational', uptime: 99.9 },
        { name: 'Email Service', status: 'operational', uptime: 99.8 },
        { name: 'DNS Service', status: 'operational', uptime: 100 },
        { name: 'Backup Service', status: 'maintenance', uptime: 99.5 },
        { name: 'Support System', status: 'operational', uptime: 99.9 }
      ],
      lastUpdated: new Date()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ===== HELP & TUTORIALS =====
router.get('/help/getting-started', (req, res) => {
  res.json({
    steps: [
      {
        title: 'Add Your First Website',
        description: 'Connect your domain and start building your online presence',
        completed: true,
        url: '/client/websites'
      },
      {
        title: 'Set Up Email Accounts',
        description: 'Create professional email addresses for your business',
        completed: false,
        url: '/client/email'
      },
      {
        title: 'Configure Security Settings',
        description: 'Protect your website with SSL and security features',
        completed: false,
        url: '/client/security'
      },
      {
        title: 'Enable Analytics',
        description: 'Track your website performance and visitor insights',
        completed: false,
        url: '/client/analytics'
      }
    ]
  });
});

router.get('/help/tutorials', (req, res) => {
  res.json({
    categories: [
      {
        name: 'Getting Started',
        tutorials: [
          { title: 'Setting up your first website', duration: '5 min', url: '/tutorials/first-website' },
          { title: 'Understanding your dashboard', duration: '3 min', url: '/tutorials/dashboard' },
          { title: 'Managing your subscription', duration: '4 min', url: '/tutorials/subscription' }
        ]
      },
      {
        name: 'Website Management',
        tutorials: [
          { title: 'Uploading files and content', duration: '6 min', url: '/tutorials/file-upload' },
          { title: 'Setting up SSL certificates', duration: '4 min', url: '/tutorials/ssl-setup' },
          { title: 'Configuring domain settings', duration: '5 min', url: '/tutorials/domain-config' }
        ]
      },
      {
        name: 'Email & Communication',
        tutorials: [
          { title: 'Creating email accounts', duration: '3 min', url: '/tutorials/email-accounts' },
          { title: 'Setting up email forwarding', duration: '4 min', url: '/tutorials/email-forwarding' },
          { title: 'Accessing webmail', duration: '2 min', url: '/tutorials/webmail' }
        ]
      }
    ]
  });
});

export default router;
