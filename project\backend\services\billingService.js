import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { AuditLogService } from './auditLogService.js';
import Billing from '../models/billing.js';
import Subscription from '../models/subscription.js';
import Stripe from 'stripe';

export class BillingService extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = {
      stripe: {
        secretKey: config.stripe?.secretKey || process.env.STRIPE_SECRET_KEY,
        publishableKey: config.stripe?.publishableKey || process.env.STRIPE_PUBLISHABLE_KEY,
        webhookSecret: config.stripe?.webhookSecret || process.env.STRIPE_WEBHOOK_SECRET
      },
      currency: config.currency || 'usd',
      taxRate: config.taxRate || 0.08, // 8% default tax rate
      ...config
    };

    this.auditService = new AuditLogService();
    this.stripe = null;
    this.customers = new Map();
    this.invoices = new Map();

    this.initializeStripe();
  }

  initializeStripe() {
    try {
      if (this.config.stripe.secretKey) {
        this.stripe = new Stripe(this.config.stripe.secretKey);
        logger.info('Stripe initialized successfully');
      } else {
        logger.warn('Stripe secret key not provided, billing features will be limited');
      }
    } catch (error) {
      logger.error('Error initializing Stripe:', error);
    }
  }

  // ===== STRIPE CUSTOMER MANAGEMENT =====

  async createStripeCustomer(customerData) {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const customer = await this.stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        phone: customerData.phone,
        address: customerData.address,
        metadata: {
          userId: customerData.userId,
          plan: customerData.plan || 'basic'
        }
      });

      this.customers.set(customer.id, customer);

      await this.auditService.log(
        customerData.userId,
        'billing_customer_created',
        { customerId: customer.id, email: customerData.email },
        'info'
      );

      logger.info(`Stripe customer created: ${customer.id}`);
      return customer;
    } catch (error) {
      logger.error('Error creating Stripe customer:', error);
      throw error;
    }
  }

  async processStripePayment(paymentData) {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(paymentData.amount * 100), // Convert to cents
        currency: this.config.currency,
        customer: paymentData.customerId,
        payment_method: paymentData.paymentMethodId,
        confirmation_method: 'manual',
        confirm: true,
        return_url: paymentData.returnUrl,
        metadata: {
          userId: paymentData.userId,
          description: paymentData.description
        }
      });

      await this.auditService.log(
        paymentData.userId,
        'payment_processed',
        {
          paymentIntentId: paymentIntent.id,
          amount: paymentData.amount,
          currency: this.config.currency,
          status: paymentIntent.status
        },
        'info'
      );

      logger.info(`Stripe payment processed: ${paymentIntent.id}`);
      return paymentIntent;
    } catch (error) {
      logger.error('Error processing Stripe payment:', error);
      throw error;
    }
  }

  async createSubscription(userId, subscriptionId, paymentMethod, paymentDetails, billingAddress) {
    try {
      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription plan not found');
      }

      const startDate = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + (subscription.billingCycle === 'yearly' ? 12 : 1));

      const billing = new Billing({
        userId,
        subscriptionId,
        startDate,
        endDate,
        paymentMethod,
        paymentDetails,
        billingAddress
      });

      await billing.save();
      await this.processPayment(billing);

      logger.info('Subscription created successfully', {
        userId,
        subscriptionId,
        billingId: billing._id
      });

      return billing;
    } catch (error) {
      logger.error('Error creating subscription', {
        userId,
        subscriptionId,
        error: error.message
      });
      throw error;
    }
  }

  async processPayment(billing) {
    try {
      // This would integrate with a payment gateway
      // (e.g., Stripe, PayPal) to process the payment
      const invoice = billing.generateInvoice();

      // Simulate payment processing
      invoice.status = 'paid';
      invoice.paidDate = new Date();
      billing.status = 'active';

      await billing.save();

      this.emit('payment_processed', {
        billingId: billing._id,
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.amount,
        status: invoice.status
      });

      return invoice;
    } catch (error) {
      this.logger.error('Error processing payment', {
        billingId: billing._id,
        error: error.message
      });
      throw error;
    }
  }

  async updateUsageMetrics(billingId, metrics) {
    try {
      const billing = await Billing.findById(billingId);
      if (!billing) {
        throw new Error('Billing record not found');
      }

      billing.usageMetrics.instanceHours += metrics.instanceHours || 0;
      billing.usageMetrics.dataTransfer += metrics.dataTransfer || 0;
      billing.usageMetrics.apiCalls += metrics.apiCalls || 0;

      await billing.save();

      this.logger.info('Usage metrics updated', {
        billingId,
        metrics: billing.usageMetrics
      });

      return billing.usageMetrics;
    } catch (error) {
      this.logger.error('Error updating usage metrics', {
        billingId,
        error: error.message
      });
      throw error;
    }
  }

  async cancelSubscription(billingId) {
    try {
      const billing = await Billing.findById(billingId);
      if (!billing) {
        throw new Error('Billing record not found');
      }

      billing.status = 'cancelled';
      billing.autoRenew = false;
      await billing.save();

      this.logger.info('Subscription cancelled', { billingId });
      return billing;
    } catch (error) {
      this.logger.error('Error cancelling subscription', {
        billingId,
        error: error.message
      });
      throw error;
    }
  }

  async getBillingHistory(userId) {
    try {
      return await Billing.find({ userId })
        .populate('subscriptionId')
        .sort({ createdAt: -1 });
    } catch (error) {
      logger.error('Error fetching billing history', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  // ===== ADMIN REVENUE ANALYTICS =====

  async getRevenueAnalytics(timeRange = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange);

      if (!this.stripe) {
        // Return mock data if Stripe not available
        return this.getMockRevenueAnalytics(timeRange);
      }

      const charges = await this.stripe.charges.list({
        created: {
          gte: Math.floor(startDate.getTime() / 1000),
          lte: Math.floor(endDate.getTime() / 1000)
        },
        limit: 100
      });

      const analytics = {
        totalRevenue: 0,
        totalTransactions: charges.data.length,
        averageTransactionValue: 0,
        successfulPayments: 0,
        failedPayments: 0,
        refunds: 0,
        dailyRevenue: new Map(),
        topCustomers: new Map()
      };

      for (const charge of charges.data) {
        const amount = charge.amount / 100; // Convert from cents
        const date = new Date(charge.created * 1000).toDateString();

        analytics.totalRevenue += amount;

        if (charge.status === 'succeeded') {
          analytics.successfulPayments++;
        } else {
          analytics.failedPayments++;
        }

        if (charge.refunded) {
          analytics.refunds++;
        }

        // Daily revenue
        const dailyAmount = analytics.dailyRevenue.get(date) || 0;
        analytics.dailyRevenue.set(date, dailyAmount + amount);

        // Top customers
        if (charge.customer) {
          const customerAmount = analytics.topCustomers.get(charge.customer) || 0;
          analytics.topCustomers.set(charge.customer, customerAmount + amount);
        }
      }

      analytics.averageTransactionValue = analytics.totalRevenue / analytics.totalTransactions || 0;

      // Convert Maps to Arrays for easier consumption
      analytics.dailyRevenue = Array.from(analytics.dailyRevenue.entries())
        .map(([date, amount]) => ({ date, amount }))
        .sort((a, b) => new Date(a.date) - new Date(b.date));

      analytics.topCustomers = Array.from(analytics.topCustomers.entries())
        .map(([customerId, amount]) => ({ customerId, amount }))
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10);

      return analytics;
    } catch (error) {
      logger.error('Error getting revenue analytics:', error);
      throw error;
    }
  }

  getMockRevenueAnalytics(timeRange) {
    // Return mock analytics data for demonstration
    const analytics = {
      totalRevenue: 15750.00,
      totalTransactions: 45,
      averageTransactionValue: 350.00,
      successfulPayments: 42,
      failedPayments: 3,
      refunds: 2,
      dailyRevenue: [],
      topCustomers: []
    };

    // Generate mock daily revenue
    for (let i = timeRange; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      analytics.dailyRevenue.push({
        date: date.toDateString(),
        amount: Math.random() * 1000
      });
    }

    return analytics;
  }

  async processRefund(paymentIntentId, amount, userId) {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const refund = await this.stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount ? Math.round(amount * 100) : undefined // Convert to cents if specified
      });

      await this.auditService.log(
        userId,
        'refund_processed',
        {
          refundId: refund.id,
          paymentIntentId,
          amount: refund.amount / 100,
          status: refund.status
        },
        'warning'
      );

      logger.info(`Refund processed: ${refund.id}`);
      return refund;
    } catch (error) {
      logger.error('Error processing refund:', error);
      throw error;
    }
  }

  async generateInvoice(invoiceData) {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const invoice = await this.stripe.invoices.create({
        customer: invoiceData.customerId,
        collection_method: 'send_invoice',
        days_until_due: invoiceData.daysUntilDue || 30,
        metadata: {
          userId: invoiceData.userId,
          type: invoiceData.type || 'manual'
        }
      });

      // Add line items
      for (const item of invoiceData.items) {
        await this.stripe.invoiceItems.create({
          customer: invoiceData.customerId,
          invoice: invoice.id,
          amount: Math.round(item.amount * 100),
          currency: this.config.currency,
          description: item.description
        });
      }

      // Finalize and send invoice
      const finalizedInvoice = await this.stripe.invoices.finalizeInvoice(invoice.id);
      await this.stripe.invoices.sendInvoice(invoice.id);

      this.invoices.set(invoice.id, finalizedInvoice);

      await this.auditService.log(
        invoiceData.userId,
        'invoice_generated',
        {
          invoiceId: invoice.id,
          customerId: invoiceData.customerId,
          amount: finalizedInvoice.amount_due / 100
        },
        'info'
      );

      logger.info(`Invoice generated: ${invoice.id}`);
      return finalizedInvoice;
    } catch (error) {
      logger.error('Error generating invoice:', error);
      throw error;
    }
  }

  async handleWebhook(payload, signature) {
    try {
      if (!this.stripe || !this.config.stripe.webhookSecret) {
        throw new Error('Stripe webhook not configured');
      }

      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.config.stripe.webhookSecret
      );

      logger.info(`Received Stripe webhook: ${event.type}`);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object);
          break;
        default:
          logger.info(`Unhandled webhook event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      logger.error('Error handling webhook:', error);
      throw error;
    }
  }

  async handlePaymentSucceeded(paymentIntent) {
    await this.auditService.log(
      paymentIntent.metadata?.userId,
      'payment_succeeded',
      { paymentIntentId: paymentIntent.id, amount: paymentIntent.amount / 100 },
      'info'
    );
  }

  async handlePaymentFailed(paymentIntent) {
    await this.auditService.log(
      paymentIntent.metadata?.userId,
      'payment_failed',
      { paymentIntentId: paymentIntent.id, amount: paymentIntent.amount / 100 },
      'error'
    );
  }

  async handleInvoicePaymentSucceeded(invoice) {
    await this.auditService.log(
      null,
      'invoice_payment_succeeded',
      { invoiceId: invoice.id, amount: invoice.amount_paid / 100 },
      'info'
    );
  }

  async handleInvoicePaymentFailed(invoice) {
    await this.auditService.log(
      null,
      'invoice_payment_failed',
      { invoiceId: invoice.id, amount: invoice.amount_due / 100 },
      'error'
    );
  }
}

// Export as singleton for backward compatibility, but also export the class
export default new BillingService();